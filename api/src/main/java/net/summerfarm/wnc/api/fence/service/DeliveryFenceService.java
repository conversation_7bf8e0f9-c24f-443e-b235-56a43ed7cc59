package net.summerfarm.wnc.api.fence.service;

import net.summerfarm.wnc.api.fence.dto.*;
import net.summerfarm.wnc.api.fence.dto.area.AreaDTO;
import net.summerfarm.wnc.api.fence.dto.area.SimpleAreaDTO;
import net.summerfarm.wnc.common.query.fence.AddressQuery;
import net.summerfarm.wnc.common.query.fence.DeliveryFenceDateQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.common.query.fence.NeedOrderTimeQuery;
import net.summerfarm.wnc.common.query.warehouse.StoreNoQuery;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-06-19
 **/
public interface DeliveryFenceService {

	/**
	 * 查询配送日期
	 *
	 * @param deliveryFenceDateQuery
	 * @return
	 */
	DeliveryFenceDTO queryDeliveryDateInfo(DeliveryFenceDateQuery deliveryFenceDateQuery);

	/**
	 * 查询围栏信息
	 * @param fenceQuery
	 * @return
	 */
	FenceDTO queryDeliveryFence(FenceQuery fenceQuery);


	/**
	 * 根据条件查询截单时间
	 *
	 * @param deliveryFenceCloseTimeQueryDTO
	 * @return
	 */
	LocalTime queryCloseTime(DeliveryFenceCloseTimeQuery deliveryFenceCloseTimeQueryDTO);

	/**
	 * 根据城市查询所在围栏等信息
	 *
	 * @param cityList 城市
	 * @return
	 */
	List<SimpleAreaDTO> queryAreaLegitimacy(List<String> cityList);


	/**
	 * 根据省市区查询区域信息
	 *
	 * @param city     城市
	 * @param area     区域
	 * @return
	 */
	AreaDTO queryAreaByAddress( String city, String area);

	/**
	 * 查询城配仓编号
	 * @param storeNoQuery 查询条件
	 * @return 结果
	 */
	FenceDTO queryStoreNo(StoreNoQuery storeNoQuery);
	/**
	 * 处理sku配送相关信息
	 * @param skus sku集合
	 * @param deliveryFenceDTO 围栏信息
	 * @return sku配送信息
	 */
	List<SkuDeliveryDateDTO> handleSkuDeliveryInfo(List<String> skus, DeliveryFenceDTO deliveryFenceDTO);

	/**
	 * 查询sku配送日期相关信息
	 * @param query 查询
	 * @return 配送日期结果
	 */
    DeliveryFenceDTO querySkuDeliveryDateInfo(DeliveryFenceDateQuery query);

	/**
	 * 处理可配送日期
	 * @param wantDeliveryTime 查询想配送日期集合
	 * @param deliveryFenceDTO 配送信息
	 */
	void handleWantDeliveryDate(List<LocalDate> wantDeliveryTime, DeliveryFenceDTO deliveryFenceDTO);

	/**
	 * 根据城市集合查询配送围栏信息
	 * @param cityList 行政市集合
	 * @return 配送围栏信息
	 */
	List<FenceDTO> batchQueryDeliveryFenceByCity(List<String> cityList);

	/**
	 * 根据地址查询地址归属围栏信息
	 * @param addressQueryList 联地址集合
	 * @return 地址归属围栏信息
	 */
	List<AddressBelongFenceDTO> batchQueryAddressBelongFence(List<AddressQuery> addressQueryList);

	/**
	 * 查询围栏信息 覆盖配送区域
	 * @param fenceQuery 查询
	 * @return 结果
	 */
	List<FenceDTO> queryFenceListWithArea(FenceQuery fenceQuery);

	/**
	 * 找出需要下单时间查询配送日期的SKU集合
	 * @param query 查询
	 * @return 结果
	 */
	List<String> findNeedOrderTimeQueryDeliveryDateSkuList(NeedOrderTimeQuery query);
}
