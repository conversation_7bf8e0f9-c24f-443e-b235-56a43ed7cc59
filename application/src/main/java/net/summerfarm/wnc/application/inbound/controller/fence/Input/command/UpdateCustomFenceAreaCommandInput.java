package net.summerfarm.wnc.application.inbound.controller.fence.Input.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增自定义区域
 * date: 2025/8/27 11:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class UpdateCustomFenceAreaCommandInput {

    /**
     * 围栏区域变更ID
     */
    @NotNull(message = "围栏区域变更ID不能为空")
    private Long fenceAreaChangeRecordsId;

    /**
     * 围栏变更记录ID
     */
    @NotNull(message = "围栏变更记录ID不能为空")
    private Long fenceChangeRecordsId;

    /**
     * 区域编码
     */
    @NotBlank(message = "区域编码不能为空")
    private String adCode;

    /**
     * 省
     */
    @NotBlank(message = "区域行政省不能为空")
    private String province;

    /**
     * 市
     */
    @NotBlank(message = "区域行政市不能为空")
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 等级
     */
    @NotBlank(message = "区域等级不能为空")
    private String level;

    /**
     * 坐标POI图形
     */
    @NotBlank(message = "区域等级不能为空")
    private String geoShape;

    /**
     * 自定义区域名称
     */
    @NotBlank(message = "自定义区域名称不能为空")
    private String customAreaName;
}
