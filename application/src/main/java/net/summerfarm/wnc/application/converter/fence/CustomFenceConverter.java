package net.summerfarm.wnc.application.converter.fence;

import net.summerfarm.wnc.application.inbound.controller.fence.dto.CustomCityAreaDTO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomFenceAreaVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaDetailVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaDetailVO.CustomFenceDetailVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaDetailVO.CustomAreaDetail;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义围栏转换器
 * date: 2025/8/29<br/>
 *
 * <AUTHOR> />
 */
public class CustomFenceConverter {

    /**
     * 转换Entity为CustomCityAreaDTO
     */
    public static CustomCityAreaDTO convertToCustomCityAreaDTO(WncCityAreaChangeWarehouseRecordsEntity entity) {
        if (entity == null) {
            return null;
        }
        
        CustomCityAreaDTO dto = new CustomCityAreaDTO();
        dto.setCityAreaChangeWarehouseRecordId(entity.getId());
        dto.setProvince(entity.getProvince());
        dto.setCity(entity.getCity());
        dto.setArea(entity.getArea());
        dto.setChangeStatus(entity.getChangeStatus());
        dto.setPreExeTime(entity.getPreExeTime());
        dto.setFenceChangeBatchNo(entity.getFenceChangeBatchNo());
        dto.setFenceChangeTaskId(entity.getFenceChangeTaskId());
        dto.setEffectiveTime(entity.getEffectiveTime());
        dto.setOverTime(entity.getOverTime());
        dto.setAreaDefinationType(entity.getAreaDefinationType());
        return dto;
    }

    /**
     * 转换DTO为CustomCityAreaVO
     */
    public static CustomCityAreaVO convertToCustomCityAreaVO(CustomCityAreaDTO dto) {
        if (dto == null) {
            return null;
        }
        
        CustomCityAreaVO vo = new CustomCityAreaVO();
        vo.setCityAreaChangeWarehouseRecordId(dto.getCityAreaChangeWarehouseRecordId());
        vo.setProvince(dto.getProvince());
        vo.setCity(dto.getCity());
        vo.setArea(dto.getArea());
        vo.setCityAreaChangeStatus(dto.getChangeStatus());
        vo.setEffectiveTime(dto.getEffectiveTime());
        vo.setOverTime(dto.getOverTime());
        vo.setPreExeTime(dto.getPreExeTime());
        // 这里可以根据需要添加其他字段的转换，如围栏数量等
        return vo;
    }

    /**
     * 转换为CustomFenceAreaVO
     */
    public static CustomFenceAreaVO convertToCustomFenceAreaVO(WncFenceChangeRecordsEntity fenceChangeRecord, 
                                                              WncFenceAreaChangeRecordsEntity areaChangeRecord) {
        if (fenceChangeRecord == null || areaChangeRecord == null) {
            return null;
        }
        
        CustomFenceAreaVO vo = new CustomFenceAreaVO();
        vo.setFenceName(fenceChangeRecord.getFenceName());
        vo.setCustomAreaName(areaChangeRecord.getCustomAreaName());
        vo.setGeoShape(areaChangeRecord.getGeoShape());
        vo.setFenceStatus(fenceChangeRecord.getFenceDetailEntity() != null ? 
                         fenceChangeRecord.getFenceDetailEntity().getStatus() : null);
        vo.setFenceStoreNo(fenceChangeRecord.getFenceStoreNo());
        vo.setFenceAreaNo(fenceChangeRecord.getFenceAreaNo());
        vo.setFenceStoreName(fenceChangeRecord.getFenceStoreName());
        vo.setFenceAreaName(fenceChangeRecord.getFenceAreaName());
        return vo;
    }

    /**
     * 转换为CustomFenceDetailVO
     */
    public static CustomFenceDetailVO convertToCustomFenceDetailVO(WncFenceChangeRecordsEntity fenceChangeRecord,
                                                                  List<CustomAreaDetail> customAreaDetails) {
        if (fenceChangeRecord == null) {
            return null;
        }
        
        CustomFenceDetailVO fenceDetailVO = new CustomFenceDetailVO();
        fenceDetailVO.setFenceChangeRecordsId(fenceChangeRecord.getId());
        fenceDetailVO.setFenceId(fenceChangeRecord.getFenceId().intValue());
        fenceDetailVO.setFenceName(fenceChangeRecord.getFenceName());
        fenceDetailVO.setFenceStatus(fenceChangeRecord.getFenceDetailEntity() != null ? 
                                    fenceChangeRecord.getFenceDetailEntity().getStatus() : null);
        fenceDetailVO.setStoreName(fenceChangeRecord.getFenceStoreName());
        fenceDetailVO.setAreaName(fenceChangeRecord.getFenceAreaName());
        fenceDetailVO.setCustomAreaDetails(customAreaDetails != null ? customAreaDetails : new ArrayList<>());
        
        return fenceDetailVO;
    }

    /**
     * 转换为CustomAreaDetail
     */
    public static CustomAreaDetail convertToCustomAreaDetail(WncFenceAreaChangeRecordsEntity areaChangeRecord) {
        if (areaChangeRecord == null) {
            return null;
        }
        
        CustomAreaDetail areaDetail = new CustomAreaDetail();
        areaDetail.setFenceAreaChangeRecordsId(areaChangeRecord.getId());
        areaDetail.setAdCodeMsgId(areaChangeRecord.getAdCodeMsgId().intValue());
        areaDetail.setCustomAreaName(areaChangeRecord.getCustomAreaName());
        areaDetail.setAdCodeMsgStatus(areaChangeRecord.getAdCodeMsgDetailEntity() != null ? 
                                     areaChangeRecord.getAdCodeMsgDetailEntity().getStatus() : null);
        areaDetail.setGeoShape(areaChangeRecord.getGeoShape());
        
        return areaDetail;
    }

    /**
     * 转换为CustomCityAreaDetailVO
     */
    public static CustomCityAreaDetailVO convertToCustomCityAreaDetailVO(WncCityAreaChangeWarehouseRecordsEntity cityAreaRecord,
                                                                        List<CustomFenceDetailVO> customFenceDetails) {
        if (cityAreaRecord == null) {
            return null;
        }
        
        CustomCityAreaDetailVO result = new CustomCityAreaDetailVO();
        result.setCityAreaChangeWarehouseRecordId(cityAreaRecord.getId());
        result.setProvince(cityAreaRecord.getProvince());
        result.setCity(cityAreaRecord.getCity());
        result.setArea(cityAreaRecord.getArea());
        result.setCustomFenceDetails(customFenceDetails != null ? customFenceDetails : new ArrayList<>());
        
        return result;
    }

    /**
     * 批量转换Entity列表为DTO列表
     */
    public static List<CustomCityAreaDTO> convertToCustomCityAreaDTOList(List<WncCityAreaChangeWarehouseRecordsEntity> entities) {
        if (entities == null) {
            return new ArrayList<>();
        }
        
        List<CustomCityAreaDTO> dtoList = new ArrayList<>();
        for (WncCityAreaChangeWarehouseRecordsEntity entity : entities) {
            CustomCityAreaDTO dto = convertToCustomCityAreaDTO(entity);
            if (dto != null) {
                dtoList.add(dto);
            }
        }
        return dtoList;
    }

    /**
     * 批量转换DTO列表为VO列表
     */
    public static List<CustomCityAreaVO> convertToCustomCityAreaVOList(List<CustomCityAreaDTO> dtos) {
        if (dtos == null) {
            return new ArrayList<>();
        }
        
        List<CustomCityAreaVO> voList = new ArrayList<>();
        for (CustomCityAreaDTO dto : dtos) {
            CustomCityAreaVO vo = convertToCustomCityAreaVO(dto);
            if (vo != null) {
                voList.add(vo);
            }
        }
        return voList;
    }

    /**
     * 批量转换为CustomAreaDetail列表
     */
    public static List<CustomAreaDetail> convertToCustomAreaDetailList(List<WncFenceAreaChangeRecordsEntity> areaChangeRecords) {
        if (areaChangeRecords == null) {
            return new ArrayList<>();
        }
        
        List<CustomAreaDetail> result = new ArrayList<>();
        for (WncFenceAreaChangeRecordsEntity areaChangeRecord : areaChangeRecords) {
            CustomAreaDetail areaDetail = convertToCustomAreaDetail(areaChangeRecord);
            if (areaDetail != null) {
                result.add(areaDetail);
            }
        }
        return result;
    }
}
