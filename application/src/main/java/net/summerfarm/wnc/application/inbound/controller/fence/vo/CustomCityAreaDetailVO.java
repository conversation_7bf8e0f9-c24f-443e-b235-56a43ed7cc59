package net.summerfarm.wnc.application.inbound.controller.fence.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 自定义围栏详情
 * date: 2025/8/26 17:32<br/>
 *
 * <AUTHOR> />
 */
@Data
public class  CustomCityAreaDetailVO {

    /**
     * 自定义城市区域变更记录ID
     */
    private Long cityAreaChangeWarehouseRecordId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 区域名称
     */
    private String area;

    /**
     * 自定义围栏
     */
    private List<CustomFenceDetailVO> customFenceDetails;

}

@Data
class CustomFenceDetailVO{

    /**
     * 围栏变更记录ID
     */
    private Long fenceChangeRecordsId;

    /**
     * 围栏ID
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 围栏状态 0正常  1失效  2删除  3暂停
     */
    private Integer fenceStatus;

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * 自定义区域详情
     */
    private List<CustomAreaDetail> customAreaDetails;
}

@Data
class CustomAreaDetail{
    /**
     * 围栏区域变更ID
     */
    private Long fenceAreaChangeRecordsId;

    /**
     * 地区ID
     */
    private Integer adCodeMsgId;

    /**
     * 自定义区域名称
     */
    private String customAreaName;

    /**
     * 区域状态 0 正常 1 失效 2删除 3停用
     */
    private Integer adCodeMsgStatus;

    /**
     * geo-shape 图形集合
     */
    private String geoShape;
}
