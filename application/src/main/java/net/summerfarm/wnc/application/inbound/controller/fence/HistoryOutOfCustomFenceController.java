package net.summerfarm.wnc.application.inbound.controller.fence;

import net.summerfarm.wnc.api.fence.input.ContactPoiDistanceCommand;
import net.summerfarm.wnc.api.fence.service.CustomFenceService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 历史过时自定义围栏（已废弃）
 * date: 2023/12/6 10:26<br/>
 *
 * <AUTHOR> />
 */
@Deprecated
@RestController
@RequestMapping("/history-out-custom-fence")
public class HistoryOutOfCustomFenceController {

    @Resource
    private CustomFenceService customFenceService;

    /**
     * 新增索引
     */
    @PostMapping("/upsert/create-custom-fence-area-index")
    public CommonResult<Void> createCustomFenceAreaIndex() {
        customFenceService.createCustomFenceAreaIndex();
        return CommonResult.ok();
    }
    /**
     * 初始化历史自定义围栏
     */
    @PostMapping("/upsert/init-custom-fence")
    public CommonResult<Void> initCustomFence() {
        customFenceService.initCustomFence();
        return CommonResult.ok();
    }

    /**
     * 区域补充相关数据
     */
    @PostMapping("/upsert/ad-code-fence-init")
    public CommonResult<Void> adCodeFenceInit(String adCode,String province,String city,String area) {
        customFenceService.adCodeFenceInit(adCode,province,city,area);
        return CommonResult.ok();
    }

    /**
     * 初始化历史自定义围栏
     */
    @PostMapping("/query/poi-fence")
    public CommonResult<String> queryPoiFence(String poi) {
        return CommonResult.ok(customFenceService.queryPoiFence(poi));
    }

    /**
     * 比较客户POI和围栏是否匹配
     * @param beginNum 开始
     * @param endNum 最后
     * @return
     */
    @PostMapping("/upsert/compare-contact-poi")
    public CommonResult<String> compareContactPoi(Integer beginNum, Integer endNum) {
        customFenceService.compareContactPoi(beginNum,endNum);
        return CommonResult.ok();
    }

    /**
     * 计算客户POI和高德的距离计算
     */
    @PostMapping("/upsert/contact-poi-distance")
    public CommonResult<Void> contactPoiDistance(@RequestBody ContactPoiDistanceCommand contactPoiDistanceCommand) {
        customFenceService.contactPoiDistance(contactPoiDistanceCommand);
        return CommonResult.ok();
    }


    /**
     * 清空历史数据
     */
    @PostMapping("/upsert/delete")
    public CommonResult<Void> deleteContactPoiDistance() {
        customFenceService.deleteContactPoiDistance();
        return CommonResult.ok();
    }
}
