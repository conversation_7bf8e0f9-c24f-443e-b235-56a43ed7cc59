package net.summerfarm.wnc.application.service.fence.impl;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.dto.CustomCityAreaDTO;
import net.summerfarm.wnc.application.service.fence.CustomFenceQueryService;
import net.summerfarm.wnc.domain.fence.param.query.CustomCityAreaQueryParam;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2025/8/26 17:30<br/>
 *
 * <AUTHOR> />
 */
public class CustomFenceQueryServiceImpl implements CustomFenceQueryService {

    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;

    @Override
    public PageInfo<CustomCityAreaDTO> queryPage(CustomCityAreaQueryInput input) {
        CustomCityAreaQueryParam queryParam = new CustomCityAreaQueryParam();
        queryParam.setFenceName(input.getFenceName());
        queryParam.setStoreNo(input.getStoreNo());
        queryParam.setAreaNo(input.getAreaNo());
        queryParam.setCityAreaChangeStatus(input.getCityAreaChangeStatus());
        queryParam.setCity(input.getCity());
        queryParam.setArea(input.getArea());
        queryParam.setPageIndex(input.getPageIndex());
        queryParam.setPageSize(input.getPageSize());
        //wncCityAreaChangeWarehouseRecordsQueryRepository.queryPage(queryParam);
        return null;
    }
}
