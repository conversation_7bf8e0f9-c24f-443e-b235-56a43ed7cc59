package net.summerfarm.wnc.application.service.fence.impl;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CityAreaFenceAreaDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.dto.CustomCityAreaDTO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaDetailVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomFenceAreaVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaDetailVO.CustomFenceDetailVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaDetailVO.CustomAreaDetail;
import net.summerfarm.wnc.application.service.fence.CustomFenceQueryService;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.CustomCityAreaQueryParam;
import net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceAreaChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2025/8/26 17:30<br/>
 *
 * <AUTHOR> />
 */
@Service
public class CustomFenceQueryServiceImpl implements CustomFenceQueryService {

    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;

    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;

    @Resource
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;

    @Override
    public PageInfo<CustomCityAreaDTO> queryPage(CustomCityAreaQueryInput input) {
        // 构建查询参数
        WncCityAreaChangeWarehouseRecordsQueryParam queryParam = new WncCityAreaChangeWarehouseRecordsQueryParam();
        queryParam.setCity(input.getCity());
        queryParam.setArea(input.getArea());
        queryParam.setChangeStatus(input.getCityAreaChangeStatus());
        queryParam.setPageIndex(input.getPageIndex());
        queryParam.setPageSize(input.getPageSize());

        // 执行分页查询
        PageInfo<WncCityAreaChangeWarehouseRecordsEntity> entityPageInfo =
            wncCityAreaChangeWarehouseRecordsQueryRepository.getPage(queryParam);

        // 转换为DTO
        List<CustomCityAreaDTO> dtoList = new ArrayList<>();
        if (entityPageInfo.getList() != null) {
            for (WncCityAreaChangeWarehouseRecordsEntity entity : entityPageInfo.getList()) {
                CustomCityAreaDTO dto = convertToCustomCityAreaDTO(entity);
                dtoList.add(dto);
            }
        }

        // 构建返回的分页信息
        PageInfo<CustomCityAreaDTO> result = new PageInfo<>(dtoList);
        result.setTotal(entityPageInfo.getTotal());
        result.setPageNum(entityPageInfo.getPageNum());
        result.setPageSize(entityPageInfo.getPageSize());
        result.setPages(entityPageInfo.getPages());

        return result;
    }

    @Override
    public List<CustomFenceAreaVO> queryCityAreaFenceAreaDetail(CityAreaFenceAreaDetailQueryInput input) {
        // 根据城市区域变更记录ID查询主记录
        WncCityAreaChangeWarehouseRecordsEntity cityAreaRecord =
            wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(input.getCityAreaChangeWarehouseRecordId());

        if (cityAreaRecord == null) {
            return new ArrayList<>();
        }

        // 根据批次号查询围栏变更记录
        WncFenceChangeRecordsQueryParam fenceChangeParam = new WncFenceChangeRecordsQueryParam();
        fenceChangeParam.setChangeBatchNo(cityAreaRecord.getFenceChangeBatchNo());
        List<WncFenceChangeRecordsEntity> fenceChangeRecords =
            wncFenceChangeRecordsQueryRepository.selectByCondition(fenceChangeParam);

        List<CustomFenceAreaVO> result = new ArrayList<>();

        // 遍历围栏变更记录，查询对应的围栏区域变更记录
        for (WncFenceChangeRecordsEntity fenceChangeRecord : fenceChangeRecords) {
            WncFenceAreaChangeRecordsQueryParam areaChangeParam = new WncFenceAreaChangeRecordsQueryParam();
            areaChangeParam.setFenceChangeId(fenceChangeRecord.getId());
            List<WncFenceAreaChangeRecordsEntity> areaChangeRecords =
                wncFenceAreaChangeRecordsQueryRepository.selectByCondition(areaChangeParam);

            // 转换为VO
            for (WncFenceAreaChangeRecordsEntity areaChangeRecord : areaChangeRecords) {
                CustomFenceAreaVO vo = convertToCustomFenceAreaVO(fenceChangeRecord, areaChangeRecord);
                result.add(vo);
            }
        }

        return result;
    }

    @Override
    public CustomCityAreaDetailVO queryDetail(CustomCityAreaDetailQueryInput input) {
        // 根据城市区域变更记录ID查询主记录
        WncCityAreaChangeWarehouseRecordsEntity cityAreaRecord =
            wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(input.getCityAreaChangeWarehouseRecordId());

        if (cityAreaRecord == null) {
            return null;
        }

        CustomCityAreaDetailVO result = new CustomCityAreaDetailVO();
        result.setCityAreaChangeWarehouseRecordId(cityAreaRecord.getId());
        result.setProvince(cityAreaRecord.getProvince());
        result.setCity(cityAreaRecord.getCity());
        result.setArea(cityAreaRecord.getArea());

        // 查询围栏详情列表
        List<CustomFenceDetailVO> customFenceDetails = queryCustomFenceDetails(cityAreaRecord.getFenceChangeBatchNo());
        result.setCustomFenceDetails(customFenceDetails);

        return result;
    }

    /**
     * 转换为CustomCityAreaDTO
     */
    private CustomCityAreaDTO convertToCustomCityAreaDTO(WncCityAreaChangeWarehouseRecordsEntity entity) {
        CustomCityAreaDTO dto = new CustomCityAreaDTO();
        dto.setCityAreaChangeWarehouseRecordId(entity.getId());
        dto.setProvince(entity.getProvince());
        dto.setCity(entity.getCity());
        dto.setArea(entity.getArea());
        dto.setChangeStatus(entity.getChangeStatus());
        dto.setPreExeTime(entity.getPreExeTime());
        dto.setFenceChangeBatchNo(entity.getFenceChangeBatchNo());
        dto.setFenceChangeTaskId(entity.getFenceChangeTaskId());
        dto.setEffectiveTime(entity.getEffectiveTime());
        dto.setOverTime(entity.getOverTime());
        dto.setAreaDefinationType(entity.getAreaDefinationType());
        return dto;
    }

    /**
     * 转换为CustomFenceAreaVO
     */
    private CustomFenceAreaVO convertToCustomFenceAreaVO(WncFenceChangeRecordsEntity fenceChangeRecord,
                                                        WncFenceAreaChangeRecordsEntity areaChangeRecord) {
        CustomFenceAreaVO vo = new CustomFenceAreaVO();
        vo.setFenceName(fenceChangeRecord.getFenceName());
        vo.setCustomAreaName(areaChangeRecord.getCustomAreaName());
        vo.setGeoShape(areaChangeRecord.getGeoShape());
        vo.setFenceStatus(fenceChangeRecord.getFenceDetailEntity() != null ?
                         fenceChangeRecord.getFenceDetailEntity().getStatus() : null);
        vo.setFenceStoreNo(fenceChangeRecord.getFenceStoreNo());
        vo.setFenceAreaNo(fenceChangeRecord.getFenceAreaNo());
        vo.setFenceStoreName(fenceChangeRecord.getFenceStoreName());
        vo.setFenceAreaName(fenceChangeRecord.getFenceAreaName());
        return vo;
    }

    /**
     * 查询自定义围栏详情列表
     */
    private List<CustomFenceDetailVO> queryCustomFenceDetails(String fenceChangeBatchNo) {
        // 根据批次号查询围栏变更记录
        WncFenceChangeRecordsQueryParam fenceChangeParam = new WncFenceChangeRecordsQueryParam();
        fenceChangeParam.setChangeBatchNo(fenceChangeBatchNo);
        List<WncFenceChangeRecordsEntity> fenceChangeRecords =
            wncFenceChangeRecordsQueryRepository.selectByCondition(fenceChangeParam);

        List<CustomFenceDetailVO> result = new ArrayList<>();

        for (WncFenceChangeRecordsEntity fenceChangeRecord : fenceChangeRecords) {
            CustomFenceDetailVO fenceDetailVO = new CustomFenceDetailVO();
            fenceDetailVO.setFenceChangeRecordsId(fenceChangeRecord.getId());
            fenceDetailVO.setFenceId(fenceChangeRecord.getFenceId().intValue());
            fenceDetailVO.setFenceName(fenceChangeRecord.getFenceName());
            fenceDetailVO.setFenceStatus(fenceChangeRecord.getFenceDetailEntity() != null ?
                                        fenceChangeRecord.getFenceDetailEntity().getStatus() : null);
            fenceDetailVO.setStoreName(fenceChangeRecord.getFenceStoreName());
            fenceDetailVO.setAreaName(fenceChangeRecord.getFenceAreaName());

            // 查询对应的区域详情
            List<CustomAreaDetail> customAreaDetails = queryCustomAreaDetails(fenceChangeRecord.getId());
            fenceDetailVO.setCustomAreaDetails(customAreaDetails);

            result.add(fenceDetailVO);
        }

        return result;
    }

    /**
     * 查询自定义区域详情列表
     */
    private List<CustomAreaDetail> queryCustomAreaDetails(Long fenceChangeId) {
        WncFenceAreaChangeRecordsQueryParam areaChangeParam = new WncFenceAreaChangeRecordsQueryParam();
        areaChangeParam.setFenceChangeId(fenceChangeId);
        List<WncFenceAreaChangeRecordsEntity> areaChangeRecords =
            wncFenceAreaChangeRecordsQueryRepository.selectByCondition(areaChangeParam);

        List<CustomAreaDetail> result = new ArrayList<>();

        for (WncFenceAreaChangeRecordsEntity areaChangeRecord : areaChangeRecords) {
            CustomAreaDetail areaDetail = new CustomAreaDetail();
            areaDetail.setFenceAreaChangeRecordsId(areaChangeRecord.getId());
            areaDetail.setAdCodeMsgId(areaChangeRecord.getAdCodeMsgId().intValue());
            areaDetail.setCustomAreaName(areaChangeRecord.getCustomAreaName());
            areaDetail.setAdCodeMsgStatus(areaChangeRecord.getAdCodeMsgDetailEntity() != null ?
                                         areaChangeRecord.getAdCodeMsgDetailEntity().getStatus() : null);
            areaDetail.setGeoShape(areaChangeRecord.getGeoShape());

            result.add(areaDetail);
        }

        return result;
    }
}
