package net.summerfarm.wnc.application.inbound.controller.fence.vo;

import lombok.Data;

import java.util.List;

/**
 * 自定义围栏区域信息
 * date: 2025/8/26 17:46<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomFenceAreaVO {

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 自定义区域名称
     */
    private String customAreaName;

    /**
     * geo-shape 图形集合
     */
    private String geoShape;

    /**
     * 围栏状态 0正常  1失效  2删除  3暂停
     */
    private Integer fenceStatus;

    /**
     * 城配编号
     */
    private Integer fenceStoreNo;

    /**
     * 运营区域编号
     */
    private Integer fenceAreaNo;

    /**
     * 城配仓名称
     */
    private String fenceStoreName;

    /**
     * 运营区域名称
     */
    private String fenceAreaName;

}
