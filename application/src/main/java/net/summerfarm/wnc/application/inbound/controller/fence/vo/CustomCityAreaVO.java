package net.summerfarm.wnc.application.inbound.controller.fence.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 自定义围栏
 * date: 2025/8/26 17:32<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomCityAreaVO {

    /**
     * 自定义城市区域变更记录ID
     */
    private Long cityAreaChangeWarehouseRecordId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 区域名称
     */
    private String area;

    /**
     * 自定义城市区域变更状态 0 等待生效 10正常生效中 20已结束
     */
    private Integer cityAreaChangeStatus;

    /**
     * 城配仓名称
     */
    private String storeNames;

    /**
     * 运营区域名称
     */
    private String areaNames;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 结束时间
     */
    private LocalDateTime overTime;

    /**
     * 预约切仓执行时间
     */
    private LocalDateTime preExeTime;

    /**
     * 围栏数量
     */
    private Integer fenceNum;


}
