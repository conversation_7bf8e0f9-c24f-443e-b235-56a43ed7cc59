package net.summerfarm.wnc.application.inbound.controller.fence.Input.query;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Description: <br/>
 * date: 2025/8/27 18:47<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CityAreaFenceAreaDetailQueryInput {

    /**
     * 自定义城市区域变更记录ID
     */
    @NotNull(message = "自定义城市区域变更记录ID不能为空")
    private Long cityAreaChangeWarehouseRecordId;
}
