package net.summerfarm.wnc.application.service.changeTask;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.wnc.common.enums.ConfigEnums;
import net.summerfarm.wnc.domain.ConfigRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.warehouse.entity.ConfigEntity;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description:围栏切仓任务消息发送器
 * date: 2023/8/29 16:08
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FenceChangeTaskSender {

    private final ConfigRepository configRepository;

    public String queryNoticeUrl(String configKey) {
        if (StrUtil.isBlank(configKey)){
            return null;
        }
        ConfigEntity configEntity = configRepository.queryByKey(configKey);
        if (configEntity == null || StrUtil.isBlank(configEntity.getValue())){
            log.error("configKey:[{}]未找到飞书消息发送url，请检查", configKey);
            return null;
        }
        return configEntity.getValue();
    }

    /**
     * 围栏切仓任务创建消息发送
     *
     * @param fenceChangeTaskEntity 围栏切仓实体
     */
    @Async
    public void sendCreateMsg(FenceChangeTaskEntity fenceChangeTaskEntity) {
        log.info("围栏切仓任务创建消息发送");
        String url = this.queryNoticeUrl(ConfigEnums.Key.FENCE_CHANGE_TASK_CREATE.getValue());
        if (StrUtil.isBlank(url)) {
            return;
        }
        StringBuilder textMsg = new StringBuilder();
        textMsg.append("切仓任务预约成功").append("\n\n")
               .append("任务编号：").append(fenceChangeTaskEntity.getId()).append("\n\n")
               .append("切仓围栏：").append(fenceChangeTaskEntity.getFenceName()).append("\n\n")
               .append("切仓区域：").append(fenceChangeTaskEntity.getFenceAreasNameStr()).append("\n\n")
               .append("切仓说明：").append("\n")
               .append("城配仓变更：").append(fenceChangeTaskEntity.getFenceChangeRemarkVO().buildStoreChangeStr()).append("\n")
               .append("运营服务区域变更：").append(fenceChangeTaskEntity.getFenceChangeRemarkVO().buildAreaChangeStr()).append("\n")
               .append("库存仓变更：").append(fenceChangeTaskEntity.getFenceChangeRemarkVO().buildWarehouseChangeStr()).append("\n\n")
               .append("切仓时间：").append(BaseDateUtils.localDateTimeToString(fenceChangeTaskEntity.getExeTime())).append("\n\n")
               .append("操作人：").append(fenceChangeTaskEntity.getCreator()).append("\n\n")
               .append("请相关人员提前知晓，安排好切仓前的相关事宜！");
        //飞书消息通知
        FeishuBotUtil.sendMarkdownMsgAndAtAll(url,textMsg.toString());

    }

    /**
     * 围栏切仓任务取消消息发送
     *
     * @param fenceChangeTaskEntity 围栏切仓实体
     */
    @Async
    public void sendCancelMsg(FenceChangeTaskEntity fenceChangeTaskEntity) {
        log.info("围栏切仓任务取消消息发送");
        String url = this.queryNoticeUrl(ConfigEnums.Key.FENCE_CHANGE_TASK_CANCEL.getValue());
        if (url == null) {
            return;
        }

        StringBuilder textMsg = new StringBuilder();
        textMsg.append("切仓任务取消通知").append("\n\n")
                .append("任务编号：").append(fenceChangeTaskEntity.getId()).append("\n\n")
                .append("切仓围栏：").append(fenceChangeTaskEntity.getFenceName()).append("\n\n")
                .append("切仓区域：").append(fenceChangeTaskEntity.getFenceAreasNameStr()).append("\n\n")
                .append("切仓时间：").append(BaseDateUtils.localDateTimeToString(fenceChangeTaskEntity.getExeTime())).append("\n\n")
                .append("取消切仓说明：").append("当前区域城配仓为 ").append(fenceChangeTaskEntity.getFenceChangeRemarkVO().getOldStore())
                .append("，运营服务区域为 ").append(fenceChangeTaskEntity.getFenceChangeRemarkVO().getOldArea())
                .append("，库存使用仓为 ").append(fenceChangeTaskEntity.getFenceChangeRemarkVO().buildOldWarehouseStr()).append("\n\n")
                .append("操作人：").append(fenceChangeTaskEntity.getUpdater()).append("\n\n")
                .append("请相关人员知晓并安排好相关事宜！");
        //飞书消息通知
        FeishuBotUtil.sendMarkdownMsgAndAtAll(url,textMsg.toString());
    }

    /**
     * 围栏切仓任务-区域切仓阶段执行失败消息发送
     *
     * @param fenceChangeTaskEntity 围栏切仓实体
     */
    @Async
    public void sendAreaFailMsg(FenceChangeTaskEntity fenceChangeTaskEntity) {
        log.info("围栏切仓任务-区域切仓执行失败消息发送");
        String url = this.queryNoticeUrl(ConfigEnums.Key.FENCE_CHANGE_TASK_AREA_EXECUTE_FAIL.getValue());
        if (url == null) {
            return;
        }
        StringBuilder textMsg = new StringBuilder();
        textMsg.append("切仓任务失败通知").append("\n\n")
                .append("任务编号：").append(fenceChangeTaskEntity.getId()).append("\n\n")
                .append("切仓围栏：").append(fenceChangeTaskEntity.getFenceName()).append("\n\n")
                .append("切仓区域：").append(fenceChangeTaskEntity.getFenceAreasNameStr()).append("\n\n")
                .append("系统区域切换失败，请相关人员知晓并安排好相关事宜！");
        //飞书消息通知
        FeishuBotUtil.sendMarkdownMsgAndAtAll(url,textMsg.toString());
    }

    /**
     * 围栏切仓任务-订单切仓阶段执行失败消息发送
     * @param fenceChangeTaskEntity 围栏切仓实体
     * @param failCount 失败订单编号集合
     */
    @Async
    public void sendOrderFailMsg(FenceChangeTaskEntity fenceChangeTaskEntity, Integer failCount) {
        log.info("围栏切仓任务-订单切仓执行失败消息发送");
        String url = this.queryNoticeUrl(ConfigEnums.Key.FENCE_CHANGE_TASK_ORDER_EXECUTE_FAIL.getValue());
        if (url == null) {
            return;
        }
        StringBuilder textMsg = new StringBuilder();
        textMsg.append("订单切仓失败通知").append("\n\n")
                .append("任务编号：").append(fenceChangeTaskEntity.getId()).append("\n\n")
                .append("切仓围栏：").append(fenceChangeTaskEntity.getFenceName()).append("\n\n")
                .append("切仓区域：").append(fenceChangeTaskEntity.getFenceAreasNameStr()).append("\n\n")
                .append("切仓说明：").append("\n")
                .append("城配仓变更：").append(fenceChangeTaskEntity.getFenceChangeRemarkVO().buildStoreChangeStr()).append("\n")
                .append("运营服务区域变更：").append(fenceChangeTaskEntity.getFenceChangeRemarkVO().buildAreaChangeStr()).append("\n")
                .append("库存仓变更：").append(fenceChangeTaskEntity.getFenceChangeRemarkVO().buildWarehouseChangeStr()).append("\n\n")
                .append("切仓失败订单数：").append(failCount == null ? "未知" : failCount).append("\n\n")
                .append("请相关人员在履约日前处理失败订单！");
        //飞书消息通知
        FeishuBotUtil.sendMarkdownMsgAndAtAll(url,textMsg.toString());
    }
}
