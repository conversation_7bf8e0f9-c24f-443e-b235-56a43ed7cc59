package net.summerfarm.wnc.application.service.fence;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.dto.CustomCityAreaDTO;

/**
 * Description: <br/>
 * date: 2025/8/26 17:29<br/>
 *
 * <AUTHOR> />
 */
public interface CustomFenceQueryService {

    /**
     * 分页查询自定义区域列表
     * @param input
     * @return
     */
    PageInfo<CustomCityAreaDTO> queryPage(CustomCityAreaQueryInput input);
}
