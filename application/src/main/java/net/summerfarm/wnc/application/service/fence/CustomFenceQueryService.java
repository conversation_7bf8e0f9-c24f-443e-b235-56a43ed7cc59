package net.summerfarm.wnc.application.service.fence;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CityAreaFenceAreaDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.dto.CustomCityAreaDTO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomCityAreaDetailVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CustomFenceAreaVO;

import java.util.List;

/**
 * Description: <br/>
 * date: 2025/8/26 17:29<br/>
 *
 * <AUTHOR> />
 */
public interface CustomFenceQueryService {

    /**
     * 分页查询自定义区域列表
     * @param input
     * @return
     */
    PageInfo<CustomCityAreaDTO> queryPage(CustomCityAreaQueryInput input);

    /**
     * 查询城市区域围栏区域详情
     * @param input
     * @return
     */
    List<CustomFenceAreaVO> queryCityAreaFenceAreaDetail(CityAreaFenceAreaDetailQueryInput input);

    /**
     * 查询自定义区域详情
     * @param input
     * @return
     */
    CustomCityAreaDetailVO queryDetail(CustomCityAreaDetailQueryInput input);
}
