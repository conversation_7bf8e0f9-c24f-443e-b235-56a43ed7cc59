package net.summerfarm.wnc.application.inbound.controller.fence;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.command.*;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CityAreaFenceAreaDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaDetailQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.query.CustomCityAreaQueryInput;
import net.summerfarm.wnc.application.inbound.controller.fence.dto.CustomCityAreaDTO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.*;
import net.summerfarm.wnc.application.service.fence.CustomFenceCommandService;
import net.summerfarm.wnc.application.service.fence.CustomFenceQueryService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 自定义围栏
 * date: 2025/8/26 16:30<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/custom-fence")
public class CustomFenceController {

    @Resource
    private CustomFenceCommandService customFenceCommandService;
    @Resource
    private CustomFenceQueryService customFenceQueryService;

    /**
     * 分页查询自定义区域列表
     * @param input 查询
     * @return 结果
     */
    @PostMapping("/query/page")
    @RequiresPermissions(value = {"custom-fence:query-page"})
    public CommonResult<PageInfo<CustomCityAreaVO>> queryPage(@RequestBody @Validated CustomCityAreaQueryInput input) {
        PageInfo<CustomCityAreaDTO> customCityAreaDTOPageList = customFenceQueryService.queryPage(input);
        return CommonResult.ok(null);
    }

    /**
     * 分页查询自定义区域列表
     * @param input 查询
     * @return 结果
     */
    @PostMapping("/query/city-area-fence-area-detail")
    @RequiresPermissions(value = {"custom-fence:city-area-fence-area-detail"})
    public CommonResult<List<CustomFenceAreaVO>> queryCityAreaFenceAreaDetail(@RequestBody @Validated CityAreaFenceAreaDetailQueryInput input) {
        return CommonResult.ok(null);
    }

    /**
     * 查询自定义区域详情
     * @param input 查询
     * @return 结果
     */
    @PostMapping("/query/detail")
    @RequiresPermissions(value = {"custom-fence:query-detail"})
    public CommonResult<CustomCityAreaDetailVO> queryDetail(@RequestBody @Validated CustomCityAreaDetailQueryInput input) {
        return CommonResult.ok(null);
    }

    /**
     * 新增自定义区域变更记录
     * @param input 新增
     * @return 结果
     */
    @PostMapping("/upsert/add-city-area-change-warehouse-record")
    @RequiresPermissions(value = {"custom-fence:add-city-area-change-warehouse-record"})
    public CommonResult<CityAreaChangeWarehouseRecordAddVO> addCityAreaChangeWarehouseRecord(@RequestBody @Validated AddCityAreaChangeWarehouseRecordCommandInput input) {
        // 状态是等待生效
        // 对于区域没有开过的不需要存快照信息，直接创建记录即可，等预约切仓时间直接生效

        // 对于区域有新建围栏的数据生成快照信息（切仓前的快照信息,围栏变更前快照信息、围栏区域变更前快照信息)

        // 对于区域有自定义围栏需要生成快照信息（切仓前后的快照信息,围栏变更前后快照信息、围栏区域变更前后快照信息)

        return CommonResult.ok();
    }


    /**
     * 新增自定义围栏变更记录
     * @param input 新增
     * @return 结果
     */
    @PostMapping("/upsert/add-custom-fence-change-record")
    @RequiresPermissions(value = {"custom-fence:add-custom-fence-change-record"})
    public CommonResult<AddCustomFenceChangeRecordVO> addCustomFenceChangeRecord(@RequestBody @Validated AddCustomFenceChangeRecordCommandInput input) {
        // 设置围栏类型为自定义围栏

        return CommonResult.ok();
    }

    /**
     * 删除自定义围栏
     * @param input 删除
     * @return 结果
     */
    @PostMapping("/upsert/delete-custom-fence-change-record")
    @RequiresPermissions(value = {"custom-fence:delete-custom-fence-change-record"})
    public CommonResult<Void> deleteCustomFenceChangeRecord(@RequestBody @Validated DelCustomFenceChangeRecordCommandInput input) {
        // 没有围栏ID、解绑区域才能删除围栏
        return CommonResult.ok();
    }

    /**
     * 新增自定义区域
     * @param input 添加
     * @return 结果
     */
    @PostMapping("/upsert/add-custom-fence-area")
    @RequiresPermissions(value = {"custom-fence:add-custom-fence-area"})
    public CommonResult<AddCustomFenceAreaVO> addCustomFenceArea(@RequestBody @Validated AddCustomFenceAreaCommandInput input) {

        // 需要记录此区域历史围栏区域快照信息，围栏变更前
        // 需要计算围栏变更后的信息
        return CommonResult.ok();
    }


    /**
     * 更新自定义区域
     * @param input 删除
     * @return 结果
     */
    @PostMapping("/upsert/update-custom-fence-area")
    @RequiresPermissions(value = {"custom-fence:add-custom-fence-area"})
    public CommonResult<Void> updateCustomFenceArea(@RequestBody @Validated UpdateCustomFenceAreaCommandInput input) {

        // 需要计算变更后的信息
        return CommonResult.ok();
    }
}
