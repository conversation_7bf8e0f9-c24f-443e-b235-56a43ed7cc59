package net.summerfarm.wnc.application.inbound.controller.changeTask;

import net.summerfarm.wnc.application.inbound.controller.changeTask.input.command.AddCustomFenceChangeStoreNoChangeTaskCommandInput;
import net.summerfarm.wnc.application.inbound.controller.changeTask.input.command.PreExeTimeCommandInput;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 新切仓任务
 * date: 2025/8/27 16:31<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/fence-change-task-new")
public class FenceChangeTaskNewController {


    /**
     * 更新自定义围栏区域预约切仓时间
     * @param input 删除
     * @return 结果
     */
    @PostMapping("/upsert/pre-exe-time")
    @RequiresPermissions(value = {"fence-change-task-new:pre-exe-time"})
    public CommonResult<Void> preExeTime(@RequestBody @Validated PreExeTimeCommandInput input) {

        // 需要计算变更后的信息
        return CommonResult.ok();
    }


    /**
     * 自定义围栏新增城配仓切仓任务
     * @param input 删除
     * @return 结果
     */
    @PostMapping("/upsert/add-custom-fence-change-storeno")
    @RequiresPermissions(value = {"custom-fence:add-custom-fence-change-storeno"})
    public CommonResult<Void> addCustomFenceChangeStoreNoChangeTask(@RequestBody @Validated AddCustomFenceChangeStoreNoChangeTaskCommandInput input) {

        // 需要计算变更后的信息
        return CommonResult.ok();
    }

}
