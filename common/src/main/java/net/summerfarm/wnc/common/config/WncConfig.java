package net.summerfarm.wnc.common.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wnc.common.config.obj.PopCityAreaOperatingAreaMappingObj;
import net.summerfarm.wnc.common.constants.AppConsts;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-07-27
 * <p>
 * * 配置类，使用方法如下：
 * * 1. 属性名与Nacos配置文件里key保持一致
 * * 2. 属性类型支持List和Map，对应Nacos里的配置类似于:
 * * list[0]=item1
 * * list[1]=item2
 * * map.key1=value1
 * * map.key2=value2
 * * 3. 可以在属性对应的setter、getter方法里做类型或者结构的转换
 * * 4. 建议给属性设置默认值，避免应用先于Nacos发布时获取到的属性为null，Nacos有对应配置时会覆盖掉默认值
 * *
 * * <AUTHOR>
 **/
@Slf4j
@Data
@Configuration
@NacosConfigurationProperties(dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class WncConfig {

	/**
	 * saas配送时间逻辑开关,false表示saas销售订单配送时间跟随鲜沐侧销售订单时间。true表示默认配送时间
	 */
	private Boolean openDefaultSaasDeliveryTime;

	/**
	 * saas售前默认截单时间
	 */
	private String defaultSaasOrderTime;

	/**
	 * saas售后默认截单时间
	 */
	private String defaultSaasAfterSaleTime;

	/**
	 * 鲜沐售后默认截单时间
	 */
	private String defaultXmAfterSaleTime;

	/**
	 * 鲜沐样品截单时间，格式HH：mm：ss
	 */
	private String defaultXmSimpleApplyTime;

	/**
	 * 全品类开始停配日期，格式yyyy-MM-dd
	 */
	private String fullCategoryStopBeginDate;

	/**
	 * 全品类结束停配日期，格式yyyy-MM-dd
	 */
	private String fullCategoryStopEndDate;

	/**
	 * 默认最晚送达时间
	 */
	private String defaultLastDelivertTime;

	/**
	 * POP城配仓
	 */
	private String popStoreNos;

	/**
	 * POP库存仓
	 */
	private String popWarehouseNos;

	/**
	 * POP T+1特殊客户白名单列表
	 */
	private String popT1MerchantWhiteList;

	/**
	 * POP城市区域json
	 */
	private String popCityAreaJsonStr;
	/**
	 * 自营仓编号
	 */
	private String selfWarehouseNos;

	/**
	 * POP大客户ID
	 */
	private String popAdminId;

	/**
	 * POP监控配置通知机器人URL
	 */
	private String popMonitorRobotUrl;

	/**
	 * 售后跟随城配仓截单时间的城配仓编号对应的有效日期
	 */
	private String afterSaleFollowStoreCloseTimeStoreNosWithEffectiveTime;

	/**
	 * 围栏渠道白名单开关 true开启 false关闭
	 */
	private String fenceChannelBusinessWhiteConfig;

	/**
	 * 运营区域SKU多仓映射告警飞书通知URL
	 */
	private String areaSkuManyWarehouseWarningJobFeiShuNoticeUrl;

	/**
	 * POP省市区运营区域映射关系json
	 */
	private String popCityAreaOperatingAreaMappingJsonStr;

	/**
	 * 非自营仓-代销不入仓-T+2配送规则
	 */
	private String nonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleStr;

	/**
	 * 霸王茶姬租户ID集合
	 */
	private String chageeTenantIdList;

	/**
	 * POP代下单鲜沐门店id
	 */
	private String popHelpOrderMerchantIdList;

	public LocalTime defaultSaasOrderTime() {
		if (this.defaultSaasOrderTime == null) {
			return AppConsts.Saas.SAAS_ORDER_TIME;
		}
		return LocalTime.parse(this.defaultSaasOrderTime);
	}

	public LocalTime defaultSaasAfterSaleTime() {
		if (this.defaultSaasAfterSaleTime == null) {
			return AppConsts.Saas.SAAS_ORDER_TIME;
		}
		log.info("saas售后默认截单时间:" + defaultSaasAfterSaleTime);
		return LocalTime.parse(this.defaultSaasAfterSaleTime);
	}


	public LocalTime defaultXmAfterSaleTime() {
		if (this.defaultXmAfterSaleTime == null) {
			return AppConsts.XmCloseTime.AFTER_SALE_CLOSE_TIME;
		}
		return LocalTime.parse(this.defaultXmAfterSaleTime);

	}

	public LocalTime defaultXmSimpleApplyTime() {
		if (this.defaultXmSimpleApplyTime == null) {
			return AppConsts.XmCloseTime.SAMPLE_APPLY_CLOSE_TIME;
		}
		return LocalTime.parse(this.defaultXmSimpleApplyTime);
		
	}

	public LocalDate getFullCategoryStopBeginDate() {
		if(StringUtils.isBlank(this.fullCategoryStopBeginDate)){
			return null;
		}
		log.info("全品类开始停配日期:" + fullCategoryStopBeginDate);
		return LocalDate.parse(this.fullCategoryStopBeginDate);
	}

	public LocalDate getFullCategoryStopEndDate() {
		if(StringUtils.isBlank(this.fullCategoryStopEndDate)){
			return null;
		}
		log.info("全品类结束停配日期:" + fullCategoryStopEndDate);
		return LocalDate.parse(this.fullCategoryStopEndDate);
	}

	public LocalTime queryDefaultLastDelivertTime(){
		LocalTime salfDeliveryTime = LocalTime.of(14, 0);
		if(this.defaultLastDelivertTime == null){
			return salfDeliveryTime;
		}
		try {
			return LocalTime.parse(this.defaultLastDelivertTime);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("解析最晚配送时间异常",e);
			return salfDeliveryTime;
		}
	}

	public List<Integer> queryPopStoreNos() {
		log.info("pop城配仓:" + this.popStoreNos);
		if (StringUtils.isBlank(this.popStoreNos)) {
			return Collections.emptyList();
		}
		return Arrays.stream(this.popStoreNos.split(","))
				.map(Integer::parseInt)
				.collect(Collectors.toList());
	}

	public List<Long> getPopT1MerchantWhiteList() {
		log.info("pop t+1特殊客户白名单列表:" + this.popT1MerchantWhiteList);
		if (StringUtils.isBlank(this.popT1MerchantWhiteList)) {
			return Collections.emptyList();
		}
		return Arrays.stream(this.popT1MerchantWhiteList.split(","))
				.map(Long::parseLong)
				.collect(Collectors.toList());
	}

	public List<Integer> queryPopWarehouseNos() {
		log.info("pop库存仓:" + this.popWarehouseNos);
		if (StringUtils.isBlank(this.popWarehouseNos)) {
			return Collections.emptyList();
		}
		return Arrays.stream(this.popWarehouseNos.split(","))
				.map(Integer::parseInt)
				.collect(Collectors.toList());
	}

	public List<CityAreas> queryPopCityAreas() {
		log.info("pop城配仓城市区域设置:" + this.popCityAreaJsonStr);
		if (StringUtils.isBlank(this.popCityAreaJsonStr)) {
			return Collections.emptyList();
		}
		List<CityAreas> cityAreas = new ArrayList<>();
		try {
			cityAreas = JSON.parseArray(this.popCityAreaJsonStr, CityAreas.class);
		} catch (Exception e) {
			log.error("解析pop城市区域json异常", e);
		}
		return cityAreas;
	}

	public List<Integer> querySelfWarehouseNos() {
		log.info("自营仓编号:" + this.selfWarehouseNos);
		if (StringUtils.isBlank(this.selfWarehouseNos)) {
			return Collections.emptyList();
		}
		return Arrays.stream(this.selfWarehouseNos.split(","))
				.map(Integer::parseInt)
				.collect(Collectors.toList());
	}

	public List<AfterSaleFollowStoreCloseTimeStoreNosWithEffectiveTime> queryAfterSaleFollowStoreCloseTimeStoreNos() {
		log.info("售后跟随城配仓截单时间的城配仓编号和有效日期:" + this.afterSaleFollowStoreCloseTimeStoreNosWithEffectiveTime);
		if (StringUtils.isBlank(this.afterSaleFollowStoreCloseTimeStoreNosWithEffectiveTime)){
			return Collections.emptyList();
		}

		List<AfterSaleFollowStoreCloseTimeStoreNosWithEffectiveTime> afterSaleFollowStoreCloseTimeStoreNosWithEffectiveTimes = new ArrayList<>();
		try {
			afterSaleFollowStoreCloseTimeStoreNosWithEffectiveTimes = JSON.parseArray(this.afterSaleFollowStoreCloseTimeStoreNosWithEffectiveTime,
					AfterSaleFollowStoreCloseTimeStoreNosWithEffectiveTime.class);
		} catch (Exception e) {
			log.error("解析售后跟随城配仓截单时间的城配仓编号和有效日期json异常", e);
		}
		return afterSaleFollowStoreCloseTimeStoreNosWithEffectiveTimes;
	}

	/**
	 * 售后跟随城配仓截单时间的城配仓编号和有效日期
	 * @return key 城配仓 value 生效日期
	 */
	public Map<Integer,List<LocalDate>> queryAfterSaleFollowStoreCloseTimeStoreNoToEffectiveTimeMap() {
		List<AfterSaleFollowStoreCloseTimeStoreNosWithEffectiveTime> afterSaleFollowStoreCloseTimeStoreNosWithEffectiveTimes = this.queryAfterSaleFollowStoreCloseTimeStoreNos();
		if(CollectionUtils.isEmpty(afterSaleFollowStoreCloseTimeStoreNosWithEffectiveTimes)){
			return Collections.emptyMap();
		}
		return afterSaleFollowStoreCloseTimeStoreNosWithEffectiveTimes.stream()
				.collect(Collectors.toMap(AfterSaleFollowStoreCloseTimeStoreNosWithEffectiveTime::getStoreNo, AfterSaleFollowStoreCloseTimeStoreNosWithEffectiveTime::getEffectiveTime));
	}

	/**
	 * 是否开启围栏渠道白名单开关
	 * @return true 开启 false 关闭
	 */
	public boolean isFenceChannelBusinessWhiteConfigOpen(){
		return StringUtils.isNotBlank(this.fenceChannelBusinessWhiteConfig) && "true".equals(this.fenceChannelBusinessWhiteConfig);
	}

	public List<PopCityAreaOperatingAreaMappingObj> queryPopCityAreaOperatingAreaMapping() {
		log.info("pop城市区域运营区域映射关系:" + this.popCityAreaOperatingAreaMappingJsonStr);
		if (StringUtils.isBlank(this.popCityAreaOperatingAreaMappingJsonStr)) {
			return Collections.emptyList();
		}
		List<PopCityAreaOperatingAreaMappingObj> mappingObjs = new ArrayList<>();
		try {
			mappingObjs = JSON.parseArray(this.popCityAreaOperatingAreaMappingJsonStr, PopCityAreaOperatingAreaMappingObj.class);
		} catch (Exception e) {
			log.error("解析pop城市区域json异常", e);
		}
		return mappingObjs;
	}

	/**
	 * 非自营仓-代销不入仓-T+2配送规则
	 * @return 仓库编号
	 */
	public List<Integer> queryNonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleT2() {
		log.info("非自营仓-代销不入仓-T+2配送规则:" + this.nonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleStr);

		if (StringUtils.isBlank(this.nonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleStr)) {
			return Collections.emptyList();
		}

		return Arrays.stream(this.nonSelfWarehouseNoSaleWithoutWarehouseToDeliveryRuleStr.split(","))
				.map(Integer::parseInt)
				.collect(Collectors.toList());
	}

	/**
	 * 获取霸王茶姬租户id列表
	 * @return 租户id列表
	 */
	public List<Long> queryChageeTenantIdList() {
		log.info("chagee租户id列表:" + this.chageeTenantIdList);
		if (StringUtils.isBlank(this.chageeTenantIdList)) {
			return Collections.emptyList();
		}
		return Arrays.stream(this.chageeTenantIdList.split(","))
				.map(Long::parseLong)
				.collect(Collectors.toList());
	}


	/**
	 * 获取POP代下单鲜沐门店id
	 * @return 门店id集合
	 */
	public List<Long> queryPopHelpOrderMerchantIdList() {
		log.info("PopHelpOrderMerchantIdList列表:" + this.popHelpOrderMerchantIdList);
		if (StringUtils.isBlank(this.popHelpOrderMerchantIdList)) {
			return Collections.emptyList();
		}
		try {
			return Arrays.stream(this.popHelpOrderMerchantIdList.split(","))
					.map(Long::parseLong)
					.collect(Collectors.toList());
		} catch (Exception e) {
			log.error("PopHelpOrderMerchantIdList列表解析异常", e);
			return Collections.emptyList();
		}
	}
}
