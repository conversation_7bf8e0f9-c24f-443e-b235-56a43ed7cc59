package net.summerfarm.wnc.inbound.provider;

import com.alibaba.fastjson.JSON;
import net.summerfarm.wnc.client.req.AreaQueryReq;
import net.summerfarm.wnc.client.req.FenceCloseTimeQueryReq;
import net.summerfarm.wnc.client.req.StoreQueryReq;
import net.summerfarm.wnc.client.req.fence.*;
import net.summerfarm.wnc.client.resp.FenceCloseTimeResp;
import net.summerfarm.wnc.client.resp.StoreQueryResp;
import net.summerfarm.wnc.client.resp.fence.AreaResp;
import net.summerfarm.wnc.client.resp.fence.AreaWarehouseNoSkuResp;
import net.summerfarm.wnc.client.resp.fence.CityAreaBelongFenceResp;
import net.summerfarm.wnc.client.resp.fence.ContactAddressBelongFenceResp;
import net.summerfarm.wnc.inbound.provider.fence.DeliveryFenceQueryProviderImpl;
import net.summerfarm.wnc.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Description:配送围栏提供者测试类
 * date: 2024/1/8 16:26
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class DeliveryFenceQueryProviderImplTest {

    @Resource
    private DeliveryFenceQueryProviderImpl deliveryFenceQueryProvider;

    @Test
    public void testBatchQueryContactAddressBelongFence(){
        List<ContactAddressQueryReq> contactAddressQueryReqs = JSON.parseArray("[{\"addressReq\":{\"address\":\"龙章路6号\",\"area\":\"西湖区\",\"city\":\"杭州市\",\"province\":\"浙江\"},\"contactId\":342746}]", ContactAddressQueryReq.class);
        ContactAddressBatchQueryReq req = new ContactAddressBatchQueryReq();
        req.setContactAddressQueryReqList(contactAddressQueryReqs);
        DubboResponse<List<ContactAddressBelongFenceResp>> response = deliveryFenceQueryProvider.batchQueryContactAddressBelongFence(req);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }

    @Test
    public void testBatchQueryContactAddressBelongFence1(){
        List<ContactAddressQueryReq> contactAddressQueryReqs = JSON.parseArray("[{\"addressReq\":{\"address\":\"龙章路6号\",\"area\":\"\",\"city\":\"东莞市\",\"province\":\"广东\"},\"contactId\":342746},{\"addressReq\":{\"address\":\"龙章路6号\",\"city\":\"东莞市\",\"province\":\"广东\"},\"contactId\":342747},{\"addressReq\":{\"address\":\"龙章路6号\",\"area\":\"东城街道\",\"city\":\"东莞市\",\"province\":\"广东\"},\"contactId\":342748}]", ContactAddressQueryReq.class);
        ContactAddressBatchQueryReq req = new ContactAddressBatchQueryReq();
        req.setContactAddressQueryReqList(contactAddressQueryReqs);
        DubboResponse<List<ContactAddressBelongFenceResp>> response = deliveryFenceQueryProvider.batchQueryContactAddressBelongFence(req);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }

    @Test
    public void testBatchQueryContactAddressBelongFence2(){
        List<ContactAddressQueryReq> contactAddressQueryReqs = JSON.parseArray("[{\"addressReq\":{\"address\":\"龙章路6号\",\"area\":\"西湖区\",\"city\":\"杭州市\",\"province\":\"浙江\"},\"contactId\":342746},{\"addressReq\":{\"address\":\"龙章路6号\",\"area\":\"西湖区\",\"city\":\"杭州市\",\"province\":\"浙江\"},\"contactId\":342747}]", ContactAddressQueryReq.class);
        ContactAddressBatchQueryReq req = new ContactAddressBatchQueryReq();
        req.setContactAddressQueryReqList(contactAddressQueryReqs);
        DubboResponse<List<ContactAddressBelongFenceResp>> response = deliveryFenceQueryProvider.batchQueryContactAddressBelongFence(req);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }

    @Test
    public void testBatchQueryContactAddressBelongFence3(){
        List<ContactAddressQueryReq> contactAddressQueryReqs = JSON.parseArray("[{\"addressReq\":{\"address\":\"万达水果超市\",\"area\":\"\",\"city\":\"中山市\",\"province\":\"广东\"},\"contactId\":346323}]", ContactAddressQueryReq.class);
        ContactAddressBatchQueryReq req = new ContactAddressBatchQueryReq();
        req.setContactAddressQueryReqList(contactAddressQueryReqs);
        DubboResponse<List<ContactAddressBelongFenceResp>> response = deliveryFenceQueryProvider.batchQueryContactAddressBelongFence(req);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }

    @Test
    public void testBatchQueryContactAddressBelongFence4(){
        List<ContactAddressQueryReq> contactAddressQueryReqs = JSON.parseArray("[{\"addressReq\":{\"address\":\"11号\",\"area\":\"淳安县\",\"city\":\"杭州市\",\"province\":\"浙江\"},\"contactId\":343216},{\"addressReq\":{\"address\":\"新杭商务中心\",\"area\":\"余杭区\",\"city\":\"杭州市\",\"province\":\"浙江\"},\"contactId\":343544},{\"addressReq\":{\"address\":\"万达超市广场\",\"area\":\"安远县\",\"city\":\"赣州市\",\"province\":\"江西\"},\"contactId\":343573},{\"addressReq\":{\"address\":\"万达小超市\",\"area\":\"商水县\",\"city\":\"周口市\",\"province\":\"河南\"},\"contactId\":346310},{\"addressReq\":{\"address\":\"万达水果超市\",\"area\":\"\",\"city\":\"中山市\",\"province\":\"广东\"},\"contactId\":346323},{\"addressReq\":{\"address\":\"万达小广场\",\"area\":\"\",\"city\":\"仙桃市\",\"province\":\"湖北\"},\"contactId\":346324},{\"addressReq\":{\"address\":\"万达小广场\",\"area\":\"项城市\",\"city\":\"周口市\",\"province\":\"河南\"},\"contactId\":346325},{\"addressReq\":{\"address\":\"水果批发市场\",\"area\":\"\",\"city\":\"中山市\",\"province\":\"广东\"},\"contactId\":346338}]", ContactAddressQueryReq.class);
        ContactAddressBatchQueryReq req = new ContactAddressBatchQueryReq();
        req.setContactAddressQueryReqList(contactAddressQueryReqs);
        DubboResponse<List<ContactAddressBelongFenceResp>> response = deliveryFenceQueryProvider.batchQueryContactAddressBelongFence(req);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }

    @Test
    public void testBatchQueryCityAreaBelongFence(){
        List<AreaQueryReq> areaQueryReqs = JSON.parseArray("[{\"area\":\"西湖区\",\"city\":\"杭州市\"}]", AreaQueryReq.class);
        CityAreaBatchQueryReq req = new CityAreaBatchQueryReq();
        req.setAreaQueryReqList(areaQueryReqs);
        DubboResponse<List<CityAreaBelongFenceResp>> response = deliveryFenceQueryProvider.batchQueryCityAreaBelongFence(req);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }

    @Test
    public void testBatchQueryCityAreaBelongFence1(){
        List<AreaQueryReq> areaQueryReqs = JSON.parseArray("[{\"area\":\"萧山区\",\"city\":\"杭州市\"},{\"area\":\"临平区\",\"city\":\"杭州市\"}]", AreaQueryReq.class);
        CityAreaBatchQueryReq req = new CityAreaBatchQueryReq();
        req.setAreaQueryReqList(areaQueryReqs);
        DubboResponse<List<CityAreaBelongFenceResp>> response = deliveryFenceQueryProvider.batchQueryCityAreaBelongFence(req);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }

    @Test
    public void testBatchQueryCityAreaBelongFence2(){
        List<AreaQueryReq> areaQueryReqs = JSON.parseArray("[{\"area\":\"萧山区\",\"city\":\"杭州市\"},{\"area\":\"萧山区\",\"city\":\"杭州市\"}]", AreaQueryReq.class);
        CityAreaBatchQueryReq req = new CityAreaBatchQueryReq();
        req.setAreaQueryReqList(areaQueryReqs);
        DubboResponse<List<CityAreaBelongFenceResp>> response = deliveryFenceQueryProvider.batchQueryCityAreaBelongFence(req);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }

    @Test
    public void queryStoreByAddress(){
        StoreQueryReq req = new StoreQueryReq();
        req.setCity("杭州市");
        req.setArea("西湖区");
        req.setContactId(122349L);
        req.setTenantId(3L);

        DubboResponse<StoreQueryResp> response = deliveryFenceQueryProvider.queryStoreByAddress(req);
        Assertions.assertNotNull(response);
        Assertions.assertTrue(response.isSuccess());
    }


    @Test
    public void testQueryAreaByWarehouseAndSku(){
        SkuWarehouseNoQueryAreaReq req = new SkuWarehouseNoQueryAreaReq();
        req.setSku("308726340301");
        req.setWarehouseNo(373);
        DubboResponse<List<AreaResp>> listDubboResponse = deliveryFenceQueryProvider.queryAreaByWarehouseAndSku(req);
        System.out.println(listDubboResponse);
    }


    @Test
    public void testQueryAreaByListWarehouseAndSku(){
        AreaQueryWarehouseNoSkuReq req = new AreaQueryWarehouseNoSkuReq();
        SkuWarehouseNoQueryAreaReq areaReq = new SkuWarehouseNoQueryAreaReq();
        areaReq.setSku("308726340301");
        areaReq.setWarehouseNo(373);

        SkuWarehouseNoQueryAreaReq areaReq2 = new SkuWarehouseNoQueryAreaReq();
        areaReq2.setSku("858558354085");
        req.setSkuWarehouseNoQueryAreaReqList(Arrays.asList(areaReq,areaReq2));

        DubboResponse<List<AreaWarehouseNoSkuResp>> listDubboResponse = deliveryFenceQueryProvider.queryAreaByListWarehouseAndSku(req);
        System.out.println(listDubboResponse);
    }
}
