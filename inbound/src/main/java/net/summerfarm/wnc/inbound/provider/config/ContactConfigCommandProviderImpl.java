package net.summerfarm.wnc.inbound.provider.config;

import net.summerfarm.wnc.api.config.dto.ContactConfigUpdateResultDTO;
import net.summerfarm.wnc.api.config.input.ContactConfigCreateCommandInput;
import net.summerfarm.wnc.api.config.input.ContactConfigRemoveCommandInput;
import net.summerfarm.wnc.api.config.input.ContactConfigUpdateCommandInput;
import net.summerfarm.wnc.api.config.service.ContactConfigCommandService;
import net.summerfarm.wnc.client.provider.config.ContactConfigCommandProvider;
import net.summerfarm.wnc.client.req.ContactConfigRemoveCommandReq;
import net.summerfarm.wnc.client.req.storeConfig.ContactConfigAdjustBatchCommandReq;
import net.summerfarm.wnc.client.req.storeConfig.ContactConfigCreateOrUpdateCommand;
import net.summerfarm.wnc.client.resp.storeConfig.ContactConfigAdjustBatchResp;
import net.summerfarm.wnc.client.resp.storeConfig.ContactConfigAdjustResp;
import net.summerfarm.wnc.inbound.provider.config.converter.ContactConfigCommandConverter;
import net.summerfarm.wnc.inbound.provider.config.converter.ContactConfigDTOConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:联系人配置操作提供者实现
 * date: 2023/9/22 17:04
 *
 * <AUTHOR>
 */
@DubboService
public class ContactConfigCommandProviderImpl implements ContactConfigCommandProvider {

    @Resource
    private ContactConfigCommandService contactConfigCommandService;


    @Override
    public DubboResponse<Void> removeContactConfig(@Valid ContactConfigRemoveCommandReq commandReq) {
        ContactConfigRemoveCommandInput command = ContactConfigRemoveCommandInput.builder()
                .tenantId(commandReq.getTenantId())
                .outerContactId(commandReq.getContactId()).build();
        contactConfigCommandService.removeConfig(command);
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<ContactConfigAdjustBatchResp> batchAdjustContactConfig(@Valid ContactConfigAdjustBatchCommandReq batchCommandReq) {
        List<ContactConfigUpdateCommandInput> commands = batchCommandReq.getCommandReqs().stream()
                .map(ContactConfigCommandConverter::req2Command).collect(Collectors.toList());
        List<ContactConfigUpdateResultDTO> result = contactConfigCommandService.updateConfig(commands);
        List<ContactConfigAdjustResp> adjustRespList = result.stream().map(ContactConfigDTOConverter::resultDto2Resp).collect(Collectors.toList());
        ContactConfigAdjustBatchResp resp = new ContactConfigAdjustBatchResp();
        resp.setAdjustRespList(adjustRespList);
        return DubboResponse.getOK(resp);
    }

    @Override
    public DubboResponse<Void> saveOrUpdate(@Valid ContactConfigCreateOrUpdateCommand command) {
        contactConfigCommandService.saveOrUpdate(ContactConfigCreateCommandInput.builder()
                .outerContactId(command.getOuterContactId())
                .storeNo(command.getStoreNo())
                .tenantId(command.getTenantId())
                .build());
        return DubboResponse.getOK();
    }
}
