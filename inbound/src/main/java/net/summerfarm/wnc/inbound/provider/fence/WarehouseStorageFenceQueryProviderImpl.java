package net.summerfarm.wnc.inbound.provider.fence;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.dto.WarehouseStorageFenceRuleDTO;
import net.summerfarm.wnc.api.fence.factory.WarehouseStorageFenceRuleFactory;
import net.summerfarm.wnc.api.fence.service.WncWarehouseStorageFenceService;
import net.summerfarm.wnc.api.warehouse.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageFenceQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseSkuFenceReq;
import net.summerfarm.wnc.client.req.WarehouseStorageFenceQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseSkuFenceResp;
import net.summerfarm.wnc.client.resp.WarehouseStorageFenceRuleResp;
import net.summerfarm.wnc.common.base.WncAssert;
import net.summerfarm.wnc.common.query.fence.WarehouseStorageFenceRuleQuery;
import net.summerfarm.wnc.common.query.warehouse.WarehouseStorageFenceQuery;
import net.summerfarm.wnc.inbound.provider.fence.converter.WarehouseStorageDTORespConverter;
import net.summerfarm.wnc.inbound.provider.fence.converter.WncWarehouseStorageFenceRuleDTOConverter;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/4/6 14:22<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class WarehouseStorageFenceQueryProviderImpl implements WarehouseStorageFenceQueryProvider {

    @Resource
    private WncWarehouseStorageFenceService wncWarehouseStorageFenceService;
    @Resource
    private WarehouseStorageFenceRuleFactory warehouseStorageFenceRuleFactory;

    @Override
    public DubboResponse<List<WarehouseStorageFenceRuleResp>> queryWarehouseStorageFence(WarehouseStorageFenceQueryReq warehouseStorageFenceQueryReq) {
        log.info("queryWarehouseStorageFence查询围栏配送顺序请求报文{}", JSON.toJSONString(warehouseStorageFenceQueryReq));
        WncAssert.notNull(warehouseStorageFenceQueryReq.getTenantId(),"tenantId不能为空");

        List<WarehouseStorageFenceRuleDTO> warehouseStorageFenceRuleDTOS = warehouseStorageFenceRuleFactory.getService(warehouseStorageFenceQueryReq).queryWarehouseStorageFence(WarehouseStorageFenceRuleQuery.builder()
                .poi(warehouseStorageFenceQueryReq.getPoi())
                .city(warehouseStorageFenceQueryReq.getCity())
                .area(warehouseStorageFenceQueryReq.getArea())
                .skuList(warehouseStorageFenceQueryReq.getSkuList())
                .tenantId(warehouseStorageFenceQueryReq.getTenantId())
                .storeNo(warehouseStorageFenceQueryReq.getStoreNo())
                .merchantId(warehouseStorageFenceQueryReq.getMerchantId())
                .contactId(warehouseStorageFenceQueryReq.getContactId())
                .source(warehouseStorageFenceQueryReq.getSource())
                .addOrderFlag(warehouseStorageFenceQueryReq.getAddOrderFlag() != null && warehouseStorageFenceQueryReq.getAddOrderFlag())
                .build());

        List<WarehouseStorageFenceRuleResp> warehouseStorageFenceRuleResps = warehouseStorageFenceRuleDTOS.stream()
                .map(WncWarehouseStorageFenceRuleDTOConverter::dto2WarehouseStorageFenceRuleRespList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        log.info("queryWarehouseStorageFence查询自营仓围栏配送顺序响应报文{}", JSON.toJSONString(warehouseStorageFenceRuleResps));
        return DubboResponse.getOK(warehouseStorageFenceRuleResps);
    }

    @Override
    public DubboResponse<List<WarehouseSkuFenceResp>> queryWarehouseSkuFence(List<WarehouseSkuFenceReq> warehouseSkuFenceReqs) {
        log.info("queryWarehouseSkuFence根据sku和仓库信息请求报文:{}",JSON.toJSONString(warehouseSkuFenceReqs));
        if(warehouseSkuFenceReqs.size() > 20){
            log.error("queryWarehouseSkuFence 查询超过最大数量20",new BizException("queryWarehouseSkuFence 查询超过最大数量20"));
            DubboResponse.getDefaultError("查询超过最大数量20");
        }
        List<WarehouseStorageFenceQuery> warehouseStorageFenceQueries = new ArrayList<>();

        for (WarehouseSkuFenceReq warehouseSkuFenceReq : warehouseSkuFenceReqs) {
            WncAssert.notEmpty(warehouseSkuFenceReq.getWarehouseNos(),"仓库编号不能为空");
            WncAssert.hasText(warehouseSkuFenceReq.getSku(),"sku不能为空");
            WarehouseStorageFenceQuery warehouseStorageFenceQuery = new WarehouseStorageFenceQuery();
            warehouseStorageFenceQuery.setSku(warehouseSkuFenceReq.getSku());
            warehouseStorageFenceQuery.setWarehouseNos(warehouseSkuFenceReq.getWarehouseNos());

            warehouseStorageFenceQueries.add(warehouseStorageFenceQuery);
        }

        List<WarehouseStorageDTO> warehouseStorageDTOS = wncWarehouseStorageFenceService.queryWarehouseSkuFence(warehouseStorageFenceQueries);
        Map<String, List<WarehouseStorageDTO>> skuWarehouseStorageMap = warehouseStorageDTOS.stream().collect(Collectors.groupingBy(WarehouseStorageDTO::getSku));

        List<WarehouseSkuFenceResp> warehouseSkuFenceResps = new ArrayList<>();
        for (String sku : skuWarehouseStorageMap.keySet()) {
            List<WarehouseStorageDTO> warehouseStorageDTOSList = skuWarehouseStorageMap.get(sku);
            WarehouseSkuFenceResp warehouseSkuFenceResp = new WarehouseSkuFenceResp();
            warehouseSkuFenceResp.setSku(sku);
            warehouseSkuFenceResp.setWarehouseSkuFenceStorages(warehouseStorageDTOSList.stream().map(WarehouseStorageDTORespConverter::storage2SkuFenceResp).collect(Collectors.toList()));
            warehouseSkuFenceResps.add(warehouseSkuFenceResp);
        }
        log.info("queryWarehouseSkuFence根据sku和仓库信息返回信息:{}",JSON.toJSONString(warehouseSkuFenceResps));

        return DubboResponse.getOK(warehouseSkuFenceResps);
    }


   /* @PostConstruct
    public void test(){
        WarehouseStorageFenceQueryReq queryAddressSkuInventoryReqDTO = new WarehouseStorageFenceQueryReq();
        queryAddressSkuInventoryReqDTO.setTenantId(1024L);
        //queryAddressSkuInventoryReqDTO.setStoreNo(1);
        *//*queryAddressSkuInventoryReqDTO.setContactId(339034L);
        queryAddressSkuInventoryReqDTO.setMerchantId(102680L);*//*
        //queryAddressSkuInventoryReqDTO.setSource(200);
        queryAddressSkuInventoryReqDTO.setArea("雨花区");
        queryAddressSkuInventoryReqDTO.setCity("长沙市");
        queryAddressSkuInventoryReqDTO.setPoi("113.03853,28.135795");
        queryAddressSkuInventoryReqDTO.setSkuList(Arrays.asList("1052223630586","50708843171"));
        queryAddressSkuInventoryReqDTO.setAddOrderFlag(true);
        DubboResponse<List<WarehouseStorageFenceRuleResp>> listDubboResponse = queryWarehouseStorageFence(queryAddressSkuInventoryReqDTO);
        System.out.println(listDubboResponse);
    }*/
    /*@PostConstruct
    public void test(){
        List<WarehouseSkuFenceReq> warehouseSkuFenceReqs = new ArrayList<>();
        WarehouseSkuFenceReq warehouseSkuFenceReq = new WarehouseSkuFenceReq();
        warehouseSkuFenceReq.setSku("1029803584487");
        warehouseSkuFenceReq.setWarehouseNos(Arrays.asList(197));
        warehouseSkuFenceReqs.add(warehouseSkuFenceReq);
        DubboResponse<List<WarehouseSkuFenceResp>> listDubboResponse = queryWarehouseSkuFence(warehouseSkuFenceReqs);
        System.out.println(listDubboResponse);
    }*/
}
