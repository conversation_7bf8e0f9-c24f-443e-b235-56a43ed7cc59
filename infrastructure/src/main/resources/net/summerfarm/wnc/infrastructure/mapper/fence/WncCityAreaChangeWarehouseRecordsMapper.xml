<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wnc.infrastructure.mapper.fence.WncCityAreaChangeWarehouseRecordsMapper">
    <!-- 结果集映射 -->
    <resultMap id="wncCityAreaChangeWarehouseRecordsResultMap" type="net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="area" property="area" jdbcType="VARCHAR"/>
		<result column="city" property="city" jdbcType="VARCHAR"/>
		<result column="change_status" property="changeStatus" jdbcType="INTEGER"/>
		<result column="pre_exe_time" property="preExeTime" jdbcType="TIMESTAMP"/>
		<result column="fence_change_batch_no" property="fenceChangeBatchNo" jdbcType="VARCHAR"/>
		<result column="fence_change_task_id" property="fenceChangeTaskId" jdbcType="NUMERIC"/>
		<result column="effective_time" property="effectiveTime" jdbcType="TIMESTAMP"/>
		<result column="over_time" property="overTime" jdbcType="TIMESTAMP"/>
		<result column="area_defination_type" property="areaDefinationType" jdbcType="INTEGER"/>
		<result column="province" property="province" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wncCityAreaChangeWarehouseRecordsColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.area,
          t.city,
          t.change_status,
          t.pre_exe_time,
          t.fence_change_batch_no,
          t.fence_change_task_id,
          t.effective_time,
          t.over_time,
          t.area_defination_type,
          t.province
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="area != null and area !=''">
                AND t.area = #{area}
            </if>
			<if test="city != null and city !=''">
                AND t.city = #{city}
            </if>
			<if test="changeStatus != null">
                AND t.change_status = #{changeStatus}
            </if>
			<if test="preExeTime != null">
                AND t.pre_exe_time = #{preExeTime}
            </if>
			<if test="fenceChangeBatchNo != null and fenceChangeBatchNo !=''">
                AND t.fence_change_batch_no = #{fenceChangeBatchNo}
            </if>
			<if test="fenceChangeTaskId != null">
                AND t.fence_change_task_id = #{fenceChangeTaskId}
            </if>
			<if test="effectiveTime != null">
                AND t.effective_time = #{effectiveTime}
            </if>
			<if test="overTime != null">
                AND t.over_time = #{overTime}
            </if>
			<if test="areaDefinationType != null">
                AND t.area_defination_type = #{areaDefinationType}
            </if>
			<if test="province != null and province !=''">
                AND t.province = #{province}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="area != null">
                    t.area = #{area},
                </if>
                <if test="city != null">
                    t.city = #{city},
                </if>
                <if test="changeStatus != null">
                    t.change_status = #{changeStatus},
                </if>
                <if test="preExeTime != null">
                    t.pre_exe_time = #{preExeTime},
                </if>
                <if test="fenceChangeBatchNo != null">
                    t.fence_change_batch_no = #{fenceChangeBatchNo},
                </if>
                <if test="fenceChangeTaskId != null">
                    t.fence_change_task_id = #{fenceChangeTaskId},
                </if>
                <if test="effectiveTime != null">
                    t.effective_time = #{effectiveTime},
                </if>
                <if test="overTime != null">
                    t.over_time = #{overTime},
                </if>
                <if test="areaDefinationType != null">
                    t.area_defination_type = #{areaDefinationType},
                </if>
                <if test="province != null">
                    t.province = #{province},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wncCityAreaChangeWarehouseRecordsResultMap" >
        SELECT <include refid="wncCityAreaChangeWarehouseRecordsColumns" />
        FROM wnc_city_area_change_warehouse_records t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam"  resultType="net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.area area,
            t.city city,
            t.change_status changeStatus,
            t.pre_exe_time preExeTime,
            t.fence_change_batch_no fenceChangeBatchNo,
            t.fence_change_task_id fenceChangeTaskId,
            t.effective_time effectiveTime,
            t.over_time overTime,
            t.area_defination_type areaDefinationType,
            t.province province
        FROM wnc_city_area_change_warehouse_records t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam" resultMap="wncCityAreaChangeWarehouseRecordsResultMap" >
        SELECT <include refid="wncCityAreaChangeWarehouseRecordsColumns" />
        FROM wnc_city_area_change_warehouse_records t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wnc_city_area_change_warehouse_records
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="area != null">
				  area,
              </if>
              <if test="city != null">
				  city,
              </if>
              <if test="changeStatus != null">
				  change_status,
              </if>
              <if test="preExeTime != null">
				  pre_exe_time,
              </if>
              <if test="fenceChangeBatchNo != null">
				  fence_change_batch_no,
              </if>
              <if test="fenceChangeTaskId != null">
				  fence_change_task_id,
              </if>
              <if test="effectiveTime != null">
				  effective_time,
              </if>
              <if test="overTime != null">
				  over_time,
              </if>
              <if test="areaDefinationType != null">
				  area_defination_type,
              </if>
              <if test="province != null">
				  province,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="area != null">
				#{area,jdbcType=VARCHAR},
              </if>
              <if test="city != null">
				#{city,jdbcType=VARCHAR},
              </if>
              <if test="changeStatus != null">
				#{changeStatus,jdbcType=INTEGER},
              </if>
              <if test="preExeTime != null">
				#{preExeTime,jdbcType=TIMESTAMP},
              </if>
              <if test="fenceChangeBatchNo != null">
				#{fenceChangeBatchNo,jdbcType=VARCHAR},
              </if>
              <if test="fenceChangeTaskId != null">
				#{fenceChangeTaskId,jdbcType=NUMERIC},
              </if>
              <if test="effectiveTime != null">
				#{effectiveTime,jdbcType=TIMESTAMP},
              </if>
              <if test="overTime != null">
				#{overTime,jdbcType=TIMESTAMP},
              </if>
              <if test="areaDefinationType != null">
				#{areaDefinationType,jdbcType=INTEGER},
              </if>
              <if test="province != null">
				#{province,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords" >
        UPDATE wnc_city_area_change_warehouse_records t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords" >
        DELETE FROM wnc_city_area_change_warehouse_records
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>