package net.summerfarm.wnc.infrastructure.repository.fence;

import net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords;
import net.summerfarm.wnc.infrastructure.mapper.fence.WncCityAreaChangeWarehouseRecordsMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.WncCityAreaChangeWarehouseRecordsConverter;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncCityAreaChangeWarehouseRecordsCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
@Repository
public class WncCityAreaChangeWarehouseRecordsCommandRepositoryImpl implements WncCityAreaChangeWarehouseRecordsCommandRepository {

    @Autowired
    private WncCityAreaChangeWarehouseRecordsMapper wncCityAreaChangeWarehouseRecordsMapper;
    @Override
    public WncCityAreaChangeWarehouseRecordsEntity insertSelective(WncCityAreaChangeWarehouseRecordsCommandParam param) {
        WncCityAreaChangeWarehouseRecords wncCityAreaChangeWarehouseRecords = WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecords(param);
        wncCityAreaChangeWarehouseRecordsMapper.insertSelective(wncCityAreaChangeWarehouseRecords);
        return WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecordsEntity(wncCityAreaChangeWarehouseRecords);
    }

    @Override
    public int updateSelectiveById(WncCityAreaChangeWarehouseRecordsCommandParam param){
        return wncCityAreaChangeWarehouseRecordsMapper.updateSelectiveById(WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecords(param));
    }


    @Override
    public int remove(Long id) {
        return wncCityAreaChangeWarehouseRecordsMapper.remove(id);
    }
}