package net.summerfarm.wnc.infrastructure.model.fence;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Data
public class WncCityAreaChangeWarehouseRecords {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 城市
	 */
	private String area;

	/**
	 * 区域
	 */
	private String city;

	/**
	 * 变更状态 0 等待生效 10正常生效中 20已结束
	 */
	private Integer changeStatus;

	/**
	 * 预约执行切仓时间
	 */
	private LocalDateTime preExeTime;

	/**
	 * 围栏变更批次号
	 */
	private String fenceChangeBatchNo;

	/**
	 * 切仓任务ID
	 */
	private Long fenceChangeTaskId;

	/**
	 * 生效时间
	 */
	private LocalDateTime effectiveTime;

	/**
	 * 完结结束时间
	 */
	private LocalDateTime overTime;

	/**
	 * 区域定义类型10 普通范围区域  20自定义范围区域
	 */
	private Integer areaDefinationType;

	/**
	 * 省
	 */
	private String province;



}