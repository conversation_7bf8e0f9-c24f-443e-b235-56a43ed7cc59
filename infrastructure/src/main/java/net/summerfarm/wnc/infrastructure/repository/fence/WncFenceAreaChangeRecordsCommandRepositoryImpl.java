package net.summerfarm.wnc.infrastructure.repository.fence;

import net.summerfarm.wnc.infrastructure.model.fence.WncFenceAreaChangeRecords;
import net.summerfarm.wnc.infrastructure.mapper.fence.WncFenceAreaChangeRecordsMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.WncFenceAreaChangeRecordsConverter;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
@Repository
public class WncFenceAreaChangeRecordsCommandRepositoryImpl implements WncFenceAreaChangeRecordsCommandRepository {

    @Autowired
    private WncFenceAreaChangeRecordsMapper wncFenceAreaChangeRecordsMapper;
    @Override
    public WncFenceAreaChangeRecordsEntity insertSelective(WncFenceAreaChangeRecordsCommandParam param) {
        WncFenceAreaChangeRecords wncFenceAreaChangeRecords = WncFenceAreaChangeRecordsConverter.toWncFenceAreaChangeRecords(param);
        wncFenceAreaChangeRecordsMapper.insertSelective(wncFenceAreaChangeRecords);
        return WncFenceAreaChangeRecordsConverter.toWncFenceAreaChangeRecordsEntity(wncFenceAreaChangeRecords);
    }

    @Override
    public int updateSelectiveById(WncFenceAreaChangeRecordsCommandParam param){
        return wncFenceAreaChangeRecordsMapper.updateSelectiveById(WncFenceAreaChangeRecordsConverter.toWncFenceAreaChangeRecords(param));
    }


    @Override
    public int remove(Long id) {
        return wncFenceAreaChangeRecordsMapper.remove(id);
    }
}