package net.summerfarm.wnc.infrastructure.repository.fence;

import net.summerfarm.wnc.infrastructure.model.fence.WncFenceChangeRecords;
import net.summerfarm.wnc.infrastructure.mapper.fence.WncFenceChangeRecordsMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.WncFenceChangeRecordsConverter;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
@Repository
public class WncFenceChangeRecordsCommandRepositoryImpl implements WncFenceChangeRecordsCommandRepository {

    @Autowired
    private WncFenceChangeRecordsMapper wncFenceChangeRecordsMapper;
    @Override
    public WncFenceChangeRecordsEntity insertSelective(WncFenceChangeRecordsCommandParam param) {
        WncFenceChangeRecords wncFenceChangeRecords = WncFenceChangeRecordsConverter.toWncFenceChangeRecords(param);
        wncFenceChangeRecordsMapper.insertSelective(wncFenceChangeRecords);
        return WncFenceChangeRecordsConverter.toWncFenceChangeRecordsEntity(wncFenceChangeRecords);
    }

    @Override
    public int updateSelectiveById(WncFenceChangeRecordsCommandParam param){
        return wncFenceChangeRecordsMapper.updateSelectiveById(WncFenceChangeRecordsConverter.toWncFenceChangeRecords(param));
    }


    @Override
    public int remove(Long id) {
        return wncFenceChangeRecordsMapper.remove(id);
    }
}