package net.summerfarm.wnc.infrastructure.mapper.fence;

import net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords;
import net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Mapper
public interface WncCityAreaChangeWarehouseRecordsMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(WncCityAreaChangeWarehouseRecords record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(WncCityAreaChangeWarehouseRecords record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    WncCityAreaChangeWarehouseRecords selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<WncCityAreaChangeWarehouseRecords> selectByCondition(WncCityAreaChangeWarehouseRecordsQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<WncCityAreaChangeWarehouseRecordsEntity> getPage(WncCityAreaChangeWarehouseRecordsQueryParam param);
}

