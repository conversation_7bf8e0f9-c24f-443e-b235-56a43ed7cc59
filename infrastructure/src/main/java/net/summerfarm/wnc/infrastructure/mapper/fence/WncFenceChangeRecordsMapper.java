package net.summerfarm.wnc.infrastructure.mapper.fence;

import net.summerfarm.wnc.infrastructure.model.fence.WncFenceChangeRecords;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Mapper
public interface WncFenceChangeRecordsMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(WncFenceChangeRecords record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(WncFenceChangeRecords record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    WncFenceChangeRecords selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<WncFenceChangeRecords> selectByCondition(WncFenceChangeRecordsQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<WncFenceChangeRecordsEntity> getPage(WncFenceChangeRecordsQueryParam param);
}

