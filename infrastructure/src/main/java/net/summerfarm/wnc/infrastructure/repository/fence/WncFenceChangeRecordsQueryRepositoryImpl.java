package net.summerfarm.wnc.infrastructure.repository.fence;


import net.summerfarm.wnc.infrastructure.model.fence.WncFenceChangeRecords;
import net.summerfarm.wnc.infrastructure.mapper.fence.WncFenceChangeRecordsMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.WncFenceChangeRecordsConverter;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;
import net.summerfarm.wnc.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
@Repository
public class WncFenceChangeRecordsQueryRepositoryImpl implements WncFenceChangeRecordsQueryRepository {

    @Autowired
    private WncFenceChangeRecordsMapper wncFenceChangeRecordsMapper;


    @Override
    public PageInfo<WncFenceChangeRecordsEntity> getPage(WncFenceChangeRecordsQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<WncFenceChangeRecordsEntity> entities = wncFenceChangeRecordsMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public WncFenceChangeRecordsEntity selectById(Long id) {
        return WncFenceChangeRecordsConverter.toWncFenceChangeRecordsEntity(wncFenceChangeRecordsMapper.selectById(id));
    }


    @Override
    public List<WncFenceChangeRecordsEntity> selectByCondition(WncFenceChangeRecordsQueryParam param) {
        return WncFenceChangeRecordsConverter.toWncFenceChangeRecordsEntityList(wncFenceChangeRecordsMapper.selectByCondition(param));
    }

}