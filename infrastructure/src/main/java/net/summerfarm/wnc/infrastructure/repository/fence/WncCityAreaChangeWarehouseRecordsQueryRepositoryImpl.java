package net.summerfarm.wnc.infrastructure.repository.fence;


import net.summerfarm.wnc.infrastructure.model.fence.WncCityAreaChangeWarehouseRecords;
import net.summerfarm.wnc.infrastructure.mapper.fence.WncCityAreaChangeWarehouseRecordsMapper;
import net.summerfarm.wnc.infrastructure.converter.fence.WncCityAreaChangeWarehouseRecordsConverter;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam;
import net.summerfarm.wnc.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
@Repository
public class WncCityAreaChangeWarehouseRecordsQueryRepositoryImpl implements WncCityAreaChangeWarehouseRecordsQueryRepository {

    @Autowired
    private WncCityAreaChangeWarehouseRecordsMapper wncCityAreaChangeWarehouseRecordsMapper;


    @Override
    public PageInfo<WncCityAreaChangeWarehouseRecordsEntity> getPage(WncCityAreaChangeWarehouseRecordsQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<WncCityAreaChangeWarehouseRecordsEntity> entities = wncCityAreaChangeWarehouseRecordsMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public WncCityAreaChangeWarehouseRecordsEntity selectById(Long id) {
        return WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecordsEntity(wncCityAreaChangeWarehouseRecordsMapper.selectById(id));
    }


    @Override
    public List<WncCityAreaChangeWarehouseRecordsEntity> selectByCondition(WncCityAreaChangeWarehouseRecordsQueryParam param) {
        return WncCityAreaChangeWarehouseRecordsConverter.toWncCityAreaChangeWarehouseRecordsEntityList(wncCityAreaChangeWarehouseRecordsMapper.selectByCondition(param));
    }

}