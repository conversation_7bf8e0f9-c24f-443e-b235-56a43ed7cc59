package net.summerfarm.wnc.domain.fence.repository;



import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;




/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncFenceChangeRecordsCommandRepository {

    WncFenceChangeRecordsEntity insertSelective(WncFenceChangeRecordsCommandParam param);

    int updateSelectiveById(WncFenceChangeRecordsCommandParam param);

    int remove(Long id);

}