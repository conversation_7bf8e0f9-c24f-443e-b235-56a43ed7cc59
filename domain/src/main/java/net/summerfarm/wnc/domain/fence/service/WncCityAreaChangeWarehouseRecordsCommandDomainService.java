package net.summerfarm.wnc.domain.fence.service;


import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncCityAreaChangeWarehouseRecordsCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 城市区域切仓记录领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Service
public class WncCityAreaChangeWarehouseRecordsCommandDomainService {


    @Autowired
    private WncCityAreaChangeWarehouseRecordsCommandRepository wncCityAreaChangeWarehouseRecordsCommandRepository;
    @Autowired
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;



    public WncCityAreaChangeWarehouseRecordsEntity insert(WncCityAreaChangeWarehouseRecordsCommandParam param) {
        return wncCityAreaChangeWarehouseRecordsCommandRepository.insertSelective(param);
    }


    public int update(WncCityAreaChangeWarehouseRecordsCommandParam param) {
        return wncCityAreaChangeWarehouseRecordsCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return wncCityAreaChangeWarehouseRecordsCommandRepository.remove(id);
    }
}
