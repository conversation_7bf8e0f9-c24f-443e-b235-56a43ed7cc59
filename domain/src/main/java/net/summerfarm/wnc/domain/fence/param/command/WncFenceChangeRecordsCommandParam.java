package net.summerfarm.wnc.domain.fence.param.command;

import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Data
public class WncFenceChangeRecordsCommandParam {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 围栏ID
	 */
	private Long fenceId;

	/**
	 * 城配编号
	 */
	private Integer fenceStoreNo;

	/**
	 * 运营区域编号
	 */
	private Integer fenceAreaNo;

	/**
	 * 围栏其他详情信息
	 */
	private String fenceMsg;

	/**
	 * 变更批次号
	 */
	private String changeBatchNo;

	/**
	 * 围栏变更阶段 0变更前 1变更后 2结束时
	 */
	private Integer fenceChangeStage;

	/**
	 * 切仓任务ID
	 */
	private Long fenceChangeTaskId;

	/**
	 * 城配仓名称
	 */
	private String fenceStoreName;

	/**
	 * 运营区域名称
	 */
	private String fenceAreaName;

	/**
	 * 库存仓编号（多个逗号分隔）
	 */
	private String fenceWarehouseNos;

	/**
	 * 库存仓名称（多个逗号分隔）
	 */
	private String fenceWarehouseNames;

	/**
	 * 围栏名称
	 */
	private String fenceName;

	

	
}