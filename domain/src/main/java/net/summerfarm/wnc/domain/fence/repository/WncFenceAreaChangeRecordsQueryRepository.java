package net.summerfarm.wnc.domain.fence.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceAreaChangeRecordsQueryParam;



/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncFenceAreaChangeRecordsQueryRepository {

    PageInfo<WncFenceAreaChangeRecordsEntity> getPage(WncFenceAreaChangeRecordsQueryParam param);

    WncFenceAreaChangeRecordsEntity selectById(Long id);

    List<WncFenceAreaChangeRecordsEntity> selectByCondition(WncFenceAreaChangeRecordsQueryParam param);

}