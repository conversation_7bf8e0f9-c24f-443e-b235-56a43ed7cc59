package net.summerfarm.wnc.domain.fence.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam;



/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncCityAreaChangeWarehouseRecordsQueryRepository {

    PageInfo<WncCityAreaChangeWarehouseRecordsEntity> getPage(WncCityAreaChangeWarehouseRecordsQueryParam param);

    WncCityAreaChangeWarehouseRecordsEntity selectById(Long id);

    List<WncCityAreaChangeWarehouseRecordsEntity> selectByCondition(WncCityAreaChangeWarehouseRecordsQueryParam param);

}