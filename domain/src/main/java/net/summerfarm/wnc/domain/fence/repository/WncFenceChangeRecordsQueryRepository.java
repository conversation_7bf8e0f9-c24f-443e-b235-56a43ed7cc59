package net.summerfarm.wnc.domain.fence.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;



/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncFenceChangeRecordsQueryRepository {

    PageInfo<WncFenceChangeRecordsEntity> getPage(WncFenceChangeRecordsQueryParam param);

    WncFenceChangeRecordsEntity selectById(Long id);

    List<WncFenceChangeRecordsEntity> selectByCondition(WncFenceChangeRecordsQueryParam param);

}