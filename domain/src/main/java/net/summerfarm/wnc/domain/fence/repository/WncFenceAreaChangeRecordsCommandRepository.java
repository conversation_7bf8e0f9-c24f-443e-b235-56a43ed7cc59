package net.summerfarm.wnc.domain.fence.repository;



import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;




/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncFenceAreaChangeRecordsCommandRepository {

    WncFenceAreaChangeRecordsEntity insertSelective(WncFenceAreaChangeRecordsCommandParam param);

    int updateSelectiveById(WncFenceAreaChangeRecordsCommandParam param);

    int remove(Long id);

}