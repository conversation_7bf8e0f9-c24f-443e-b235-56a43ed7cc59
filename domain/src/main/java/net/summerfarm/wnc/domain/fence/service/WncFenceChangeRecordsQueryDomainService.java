package net.summerfarm.wnc.domain.fence.service;


import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsCommandRepository;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 围栏变更执行记录表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Service
public class WncFenceChangeRecordsQueryDomainService {


}
