package net.summerfarm.wnc.domain.fence;

import net.summerfarm.wnc.common.query.fence.AddressQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.domain.fence.entity.AddressBelongFenceEntity;
import net.summerfarm.wnc.domain.fence.entity.DeliveryFenceEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.entity.OutLandContactEntity;

import java.time.LocalTime;
import java.util.Collection;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/7 17:54<br/>
 *
 * <AUTHOR> />
 */
public interface DeliveryFenceRepository {

    /**
     * 查询围栏
     * @param fenceQuery 查询
     * @return 结果
     */
    List<FenceEntity> queryFenceByProvinceCityArea(FenceQuery fenceQuery);

    /**
     * 查询围栏
     * @param fenceQuery 查询
     * @return 结果
     */
    FenceEntity queryOneFenceByProvinceCityArea(FenceQuery fenceQuery);

    /**
     * 根据城配仓查询围栏信息
     * @param storeNoList 城配仓信息
     * @return 结果
     */
    List<FenceEntity> queryFenceByStoreNoList(List<Integer> storeNoList);

    /**
     * 根据城市查询围栏信息
     *
     * @param city 城市
     * @return 结果
     */
    List<FenceEntity> queryListFenceByCity(String city);

    /**
     * 根据条件查询围栏配送规则
     *
     * @param outLandContactEntity 查询条件
     * @return 配送规则
     */
    DeliveryFenceEntity queryFrequentRule(OutLandContactEntity outLandContactEntity);


    /**
     * 查询围栏主信息
     *
     * @param city 城市
     * @param area 区域
     * @return
     */
    LocalTime queryCloseTime(String city, String area);

    /**
     * 查询城配仓截单时间
     *
     * @param storeNo 城配仓编号
     * @return 截单时间
     */
    LocalTime queryStoreCloseTime(Integer storeNo);


    /**
     * 通过区域编号查询围栏列表
     *
     * @param areaNoList
     * @return
     */
    List<FenceEntity> queryListFenceByAreaNo(Collection<Integer> areaNoList);


    /**
     * 根据城市和区域查询围栏
     *
     * @param city 城市
     * @param area 区域
     * @return
     */
    FenceEntity queryFenceEntity(String city, String area);

    /**
     * 根据城市集合查询围栏信息集合
     * @param cityList 城市集合
     * @return 围栏信息集合
     */
    List<FenceEntity> queryFenceByCityList(List<String> cityList);


    /**
     * 根据地址集合查询归属围栏信息集合
     * @param addressQueryList 地址集合
     * @return 归属围栏信息集合
     */
    List<AddressBelongFenceEntity> queryAddressBelongFence(List<AddressQuery> addressQueryList);
}
