package net.summerfarm.wnc.domain.fence.repository;



import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncCityAreaChangeWarehouseRecordsCommandParam;




/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncCityAreaChangeWarehouseRecordsCommandRepository {

    WncCityAreaChangeWarehouseRecordsEntity insertSelective(WncCityAreaChangeWarehouseRecordsCommandParam param);

    int updateSelectiveById(WncCityAreaChangeWarehouseRecordsCommandParam param);

    int remove(Long id);

}